{"ast": null, "code": "import { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-2de70756\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"text-primary\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_v_card_title = _resolveComponent(\"v-card-title\");\n  const _component_v_select = _resolveComponent(\"v-select\");\n  const _component_v_col = _resolveComponent(\"v-col\");\n  const _component_v_btn = _resolveComponent(\"v-btn\");\n  const _component_v_row = _resolveComponent(\"v-row\");\n  const _component_v_chip = _resolveComponent(\"v-chip\");\n  const _component_v_data_table_server = _resolveComponent(\"v-data-table-server\");\n  const _component_v_card = _resolveComponent(\"v-card\");\n  const _component_v_list_item_title = _resolveComponent(\"v-list-item-title\");\n  const _component_v_list_item_subtitle = _resolveComponent(\"v-list-item-subtitle\");\n  const _component_v_list_item = _resolveComponent(\"v-list-item\");\n  const _component_v_list = _resolveComponent(\"v-list\");\n  const _component_v_card_subtitle = _resolveComponent(\"v-card-subtitle\");\n  const _component_v_alert = _resolveComponent(\"v-alert\");\n  const _component_v_card_text = _resolveComponent(\"v-card-text\");\n  const _component_v_spacer = _resolveComponent(\"v-spacer\");\n  const _component_v_card_actions = _resolveComponent(\"v-card-actions\");\n  const _component_v_dialog = _resolveComponent(\"v-dialog\");\n  const _component_v_snackbar = _resolveComponent(\"v-snackbar\");\n  const _component_v_container = _resolveComponent(\"v-container\");\n  return _openBlock(), _createBlock(_component_v_container, null, {\n    default: _withCtx(() => [_createVNode(_component_v_card, {\n      elevation: \"1\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_v_card_title, {\n        class: \"text-h5 pa-4\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(\" Calcul des Salaires - \" + _toDisplayString($options.monthName) + \" \" + _toDisplayString(_ctx.currentYear), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" Filters and Controls \"), _createVNode(_component_v_row, {\n        class: \"pa-4\",\n        align: \"center\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_col, {\n          cols: \"12\",\n          md: \"3\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_select, {\n            modelValue: $data.selectedMonth,\n            \"onUpdate:modelValue\": [_cache[0] || (_cache[0] = $event => $data.selectedMonth = $event), $options.fetchData],\n            items: $data.months,\n            label: \"Mois\",\n            density: \"compact\",\n            variant: \"outlined\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"items\", \"onUpdate:modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_col, {\n          cols: \"12\",\n          md: \"3\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_select, {\n            modelValue: $data.selectedYear,\n            \"onUpdate:modelValue\": [_cache[1] || (_cache[1] = $event => $data.selectedYear = $event), $options.fetchData],\n            items: $options.years,\n            label: \"Année\",\n            density: \"compact\",\n            variant: \"outlined\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"items\", \"onUpdate:modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_col, {\n          cols: \"12\",\n          md: \"3\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_select, {\n            modelValue: $data.selectedAgent,\n            \"onUpdate:modelValue\": [_cache[2] || (_cache[2] = $event => $data.selectedAgent = $event), $options.fetchData],\n            items: $options.agentOptions,\n            label: \"Agent (Optionnel)\",\n            density: \"compact\",\n            variant: \"outlined\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"items\", \"onUpdate:modelValue\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_col, {\n          cols: \"12\",\n          md: \"3\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_v_btn, {\n            onClick: $options.exportToExcel,\n            color: \"green\",\n            \"prepend-icon\": \"mdi-file-excel\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(\" Export Excel \")]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onClick\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" Data Table \"), _createVNode(_component_v_data_table_server, {\n        headers: $data.headers,\n        items: _ctx.salaryData,\n        \"items-length\": _ctx.total,\n        loading: _ctx.loading,\n        \"onUpdate:options\": $options.updateOptions,\n        class: \"elevation-1\"\n      }, {\n        \"item.baseSalary\": _withCtx(({\n          item\n        }) => [_createTextVNode(_toDisplayString($options.formatCurrency(item.baseSalary)), 1 /* TEXT */)]),\n        \"item.totalRetardFormatted\": _withCtx(({\n          item\n        }) => [_createVNode(_component_v_chip, {\n          color: $options.getRetardColor(item.totalRetardMinutes),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(item.totalRetardFormatted), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]),\n        \"item.deductedHours\": _withCtx(({\n          item\n        }) => [_createVNode(_component_v_chip, {\n          color: item.deductedHours > 0 ? 'red' : 'green',\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(item.deductedHours) + \"h \", 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]),\n        \"item.deductedAmount\": _withCtx(({\n          item\n        }) => [_createElementVNode(\"span\", {\n          class: _normalizeClass(item.deductedAmount > 0 ? 'text-red' : 'text-green')\n        }, _toDisplayString($options.formatCurrency(item.deductedAmount)), 3 /* TEXT, CLASS */)]),\n        \"item.remainingSalary\": _withCtx(({\n          item\n        }) => [_createElementVNode(\"strong\", _hoisted_1, _toDisplayString($options.formatCurrency(item.remainingSalary)), 1 /* TEXT */)]),\n        \"item.actions\": _withCtx(({\n          item\n        }) => [_createVNode(_component_v_btn, {\n          icon: \"mdi-eye\",\n          size: \"small\",\n          onClick: $event => $options.viewDetails(item),\n          variant: \"text\"\n        }, null, 8 /* PROPS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"headers\", \"items\", \"items-length\", \"loading\", \"onUpdate:options\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" Details Dialog \"), _createVNode(_component_v_dialog, {\n      modelValue: $data.detailsDialog,\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.detailsDialog = $event),\n      \"max-width\": \"800px\"\n    }, {\n      default: _withCtx(() => [$data.selectedItem ? (_openBlock(), _createBlock(_component_v_card, {\n        key: 0\n      }, {\n        default: _withCtx(() => [_createVNode(_component_v_card_title, null, {\n          default: _withCtx(() => [_createTextVNode(\" Détails - \" + _toDisplayString($data.selectedItem.name), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_text, null, {\n          default: _withCtx(() => [_createVNode(_component_v_row, null, {\n            default: _withCtx(() => [_createVNode(_component_v_col, {\n              cols: \"6\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_list, null, {\n                default: _withCtx(() => [_createVNode(_component_v_list_item, null, {\n                  default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                    default: _withCtx(() => [_createTextVNode(\"Salaire de base:\")]),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_v_list_item_subtitle, null, {\n                    default: _withCtx(() => [_createTextVNode(_toDisplayString($options.formatCurrency($data.selectedItem.baseSalary)), 1 /* TEXT */)]),\n                    _: 1 /* STABLE */\n                  })]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_list_item, null, {\n                  default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                    default: _withCtx(() => [_createTextVNode(\"Retard total:\")]),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_v_list_item_subtitle, null, {\n                    default: _withCtx(() => [_createTextVNode(_toDisplayString($data.selectedItem.totalRetardFormatted), 1 /* TEXT */)]),\n                    _: 1 /* STABLE */\n                  })]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_list_item, null, {\n                  default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                    default: _withCtx(() => [_createTextVNode(\"Heures déduites:\")]),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_v_list_item_subtitle, null, {\n                    default: _withCtx(() => [_createTextVNode(_toDisplayString($data.selectedItem.deductedHours) + \"h\", 1 /* TEXT */)]),\n                    _: 1 /* STABLE */\n                  })]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_list_item, null, {\n                  default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                    default: _withCtx(() => [_createTextVNode(\"Montant déduit:\")]),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_v_list_item_subtitle, {\n                    class: \"text-red\"\n                  }, {\n                    default: _withCtx(() => [_createTextVNode(_toDisplayString($options.formatCurrency($data.selectedItem.deductedAmount)), 1 /* TEXT */)]),\n                    _: 1 /* STABLE */\n                  })]),\n                  _: 1 /* STABLE */\n                }), _createVNode(_component_v_list_item, null, {\n                  default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                    default: _withCtx(() => [_createTextVNode(\"Salaire restant:\")]),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_v_list_item_subtitle, {\n                    class: \"text-primary font-weight-bold\"\n                  }, {\n                    default: _withCtx(() => [_createTextVNode(_toDisplayString($options.formatCurrency($data.selectedItem.remainingSalary)), 1 /* TEXT */)]),\n                    _: 1 /* STABLE */\n                  })]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_v_col, {\n              cols: \"6\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_v_card_subtitle, null, {\n                default: _withCtx(() => [_createTextVNode(\"Détail des retards par jour:\")]),\n                _: 1 /* STABLE */\n              }), $data.selectedItem.retardByDays && $data.selectedItem.retardByDays.length > 0 ? (_openBlock(), _createBlock(_component_v_list, {\n                key: 0\n              }, {\n                default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.selectedItem.retardByDays, day => {\n                  return _openBlock(), _createBlock(_component_v_list_item, {\n                    key: day.date\n                  }, {\n                    default: _withCtx(() => [_createVNode(_component_v_list_item_title, null, {\n                      default: _withCtx(() => [_createTextVNode(_toDisplayString(day.date), 1 /* TEXT */)]),\n                      _: 2 /* DYNAMIC */\n                    }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_v_list_item_subtitle, null, {\n                      default: _withCtx(() => [_createTextVNode(_toDisplayString(day.retardFormatted), 1 /* TEXT */)]),\n                      _: 2 /* DYNAMIC */\n                    }, 1024 /* DYNAMIC_SLOTS */)]),\n                    _: 2 /* DYNAMIC */\n                  }, 1024 /* DYNAMIC_SLOTS */);\n                }), 128 /* KEYED_FRAGMENT */))]),\n                _: 1 /* STABLE */\n              })) : (_openBlock(), _createBlock(_component_v_alert, {\n                key: 1,\n                type: \"success\",\n                variant: \"tonal\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(\" Aucun retard ce mois-ci \")]),\n                _: 1 /* STABLE */\n              }))]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_v_card_actions, null, {\n          default: _withCtx(() => [_createVNode(_component_v_spacer), _createVNode(_component_v_btn, {\n            color: \"blue-darken-1\",\n            variant: \"text\",\n            onClick: _cache[3] || (_cache[3] = $event => $data.detailsDialog = false)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\" Fermer \")]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" Snackbar for notifications \"), _createVNode(_component_v_snackbar, {\n      modelValue: $data.snackbar,\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.snackbar = $event),\n      color: $data.snackbarColor,\n      timeout: \"3000\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($data.snackbarText), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"color\"])]),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_v_container", "default", "_withCtx", "_createVNode", "_component_v_card", "elevation", "_component_v_card_title", "_createTextVNode", "_toDisplayString", "$options", "monthName", "_ctx", "currentYear", "_", "_createCommentVNode", "_component_v_row", "align", "_component_v_col", "cols", "md", "_component_v_select", "modelValue", "$data", "<PERSON><PERSON><PERSON><PERSON>", "$event", "fetchData", "items", "months", "label", "density", "variant", "selected<PERSON>ear", "years", "selectedAgent", "agentOptions", "clearable", "_component_v_btn", "onClick", "exportToExcel", "color", "_component_v_data_table_server", "headers", "salaryData", "total", "loading", "updateOptions", "item", "formatCurrency", "baseSalary", "_component_v_chip", "getRetardColor", "totalRetardMinutes", "size", "totalRetardFormatted", "deductedHours", "_createElementVNode", "_normalizeClass", "deductedAmount", "_hoisted_1", "remainingSalary", "icon", "viewDetails", "_component_v_dialog", "detailsDialog", "_cache", "selectedItem", "key", "name", "_component_v_card_text", "_component_v_list", "_component_v_list_item", "_component_v_list_item_title", "_component_v_list_item_subtitle", "_component_v_card_subtitle", "retardByDays", "length", "_createElementBlock", "_Fragment", "_renderList", "day", "date", "retardFormatted", "_component_v_alert", "type", "_component_v_card_actions", "_component_v_spacer", "_component_v_snackbar", "snackbar", "snackbarColor", "timeout", "snackbarText"], "sources": ["C:\\Users\\<USER>\\Desktop\\swcontrole\\SW-Controle-Front\\src\\views\\SalaryCalculation.vue"], "sourcesContent": ["<template>\n  <v-container>\n    <v-card elevation=\"1\">\n      <v-card-title class=\"text-h5 pa-4\">\n        Calcul des Salaires - {{ monthName }} {{ currentYear }}\n      </v-card-title>\n      \n      <!-- Filters and Controls -->\n      <v-row class=\"pa-4\" align=\"center\">\n        <v-col cols=\"12\" md=\"3\">\n          <v-select\n            v-model=\"selectedMonth\"\n            :items=\"months\"\n            label=\"Mois\"\n            density=\"compact\"\n            variant=\"outlined\"\n            @update:model-value=\"fetchData\"\n          ></v-select>\n        </v-col>\n        <v-col cols=\"12\" md=\"3\">\n          <v-select\n            v-model=\"selectedYear\"\n            :items=\"years\"\n            label=\"Année\"\n            density=\"compact\"\n            variant=\"outlined\"\n            @update:model-value=\"fetchData\"\n          ></v-select>\n        </v-col>\n        <v-col cols=\"12\" md=\"3\">\n          <v-select\n            v-model=\"selectedAgent\"\n            :items=\"agentOptions\"\n            label=\"Agent (Optionnel)\"\n            density=\"compact\"\n            variant=\"outlined\"\n            clearable\n            @update:model-value=\"fetchData\"\n          ></v-select>\n        </v-col>\n        <v-col cols=\"12\" md=\"3\">\n          <v-btn @click=\"exportToExcel\" color=\"green\" prepend-icon=\"mdi-file-excel\">\n            Export Excel\n          </v-btn>\n        </v-col>\n      </v-row>\n\n      <!-- Data Table -->\n      <v-data-table-server\n        :headers=\"headers\"\n        :items=\"salaryData\"\n        :items-length=\"total\"\n        :loading=\"loading\"\n        @update:options=\"updateOptions\"\n        class=\"elevation-1\"\n      >\n        <template v-slot:item.baseSalary=\"{ item }\">\n          {{ formatCurrency(item.baseSalary) }}\n        </template>\n        \n        <template v-slot:item.totalRetardFormatted=\"{ item }\">\n          <v-chip \n            :color=\"getRetardColor(item.totalRetardMinutes)\" \n            size=\"small\"\n          >\n            {{ item.totalRetardFormatted }}\n          </v-chip>\n        </template>\n        \n        <template v-slot:item.deductedHours=\"{ item }\">\n          <v-chip \n            :color=\"item.deductedHours > 0 ? 'red' : 'green'\" \n            size=\"small\"\n          >\n            {{ item.deductedHours }}h\n          </v-chip>\n        </template>\n        \n        <template v-slot:item.deductedAmount=\"{ item }\">\n          <span :class=\"item.deductedAmount > 0 ? 'text-red' : 'text-green'\">\n            {{ formatCurrency(item.deductedAmount) }}\n          </span>\n        </template>\n        \n        <template v-slot:item.remainingSalary=\"{ item }\">\n          <strong class=\"text-primary\">\n            {{ formatCurrency(item.remainingSalary) }}\n          </strong>\n        </template>\n        \n        <template v-slot:item.actions=\"{ item }\">\n          <v-btn\n            icon=\"mdi-eye\"\n            size=\"small\"\n            @click=\"viewDetails(item)\"\n            variant=\"text\"\n          ></v-btn>\n        </template>\n      </v-data-table-server>\n    </v-card>\n\n    <!-- Details Dialog -->\n    <v-dialog v-model=\"detailsDialog\" max-width=\"800px\">\n      <v-card v-if=\"selectedItem\">\n        <v-card-title>\n          Détails - {{ selectedItem.name }}\n        </v-card-title>\n        <v-card-text>\n          <v-row>\n            <v-col cols=\"6\">\n              <v-list>\n                <v-list-item>\n                  <v-list-item-title>Salaire de base:</v-list-item-title>\n                  <v-list-item-subtitle>{{ formatCurrency(selectedItem.baseSalary) }}</v-list-item-subtitle>\n                </v-list-item>\n                <v-list-item>\n                  <v-list-item-title>Retard total:</v-list-item-title>\n                  <v-list-item-subtitle>{{ selectedItem.totalRetardFormatted }}</v-list-item-subtitle>\n                </v-list-item>\n                <v-list-item>\n                  <v-list-item-title>Heures déduites:</v-list-item-title>\n                  <v-list-item-subtitle>{{ selectedItem.deductedHours }}h</v-list-item-subtitle>\n                </v-list-item>\n                <v-list-item>\n                  <v-list-item-title>Montant déduit:</v-list-item-title>\n                  <v-list-item-subtitle class=\"text-red\">{{ formatCurrency(selectedItem.deductedAmount) }}</v-list-item-subtitle>\n                </v-list-item>\n                <v-list-item>\n                  <v-list-item-title>Salaire restant:</v-list-item-title>\n                  <v-list-item-subtitle class=\"text-primary font-weight-bold\">{{ formatCurrency(selectedItem.remainingSalary) }}</v-list-item-subtitle>\n                </v-list-item>\n              </v-list>\n            </v-col>\n            <v-col cols=\"6\">\n              <v-card-subtitle>Détail des retards par jour:</v-card-subtitle>\n              <v-list v-if=\"selectedItem.retardByDays && selectedItem.retardByDays.length > 0\">\n                <v-list-item v-for=\"day in selectedItem.retardByDays\" :key=\"day.date\">\n                  <v-list-item-title>{{ day.date }}</v-list-item-title>\n                  <v-list-item-subtitle>{{ day.retardFormatted }}</v-list-item-subtitle>\n                </v-list-item>\n              </v-list>\n              <v-alert v-else type=\"success\" variant=\"tonal\">\n                Aucun retard ce mois-ci\n              </v-alert>\n            </v-col>\n          </v-row>\n        </v-card-text>\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn color=\"blue-darken-1\" variant=\"text\" @click=\"detailsDialog = false\">\n            Fermer\n          </v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <!-- Snackbar for notifications -->\n    <v-snackbar v-model=\"snackbar\" :color=\"snackbarColor\" timeout=\"3000\">\n      {{ snackbarText }}\n    </v-snackbar>\n  </v-container>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex';\nimport * as XLSX from 'xlsx';\n\nexport default {\n  name: 'SalaryCalculation',\n  data() {\n    return {\n      selectedMonth: new Date().getMonth() + 1,\n      selectedYear: new Date().getFullYear(),\n      selectedAgent: null,\n      detailsDialog: false,\n      selectedItem: null,\n      snackbar: false,\n      snackbarText: '',\n      snackbarColor: 'success',\n      options: {},\n      headers: [\n        { title: \"Agent\", key: \"name\", sortable: true },\n        { title: \"Email\", key: \"email\", sortable: false },\n        { title: \"Salaire de base\", key: \"baseSalary\", sortable: true },\n        { title: \"Retard total\", key: \"totalRetardFormatted\", sortable: false },\n        { title: \"Heures déduites\", key: \"deductedHours\", sortable: false },\n        { title: \"Montant déduit\", key: \"deductedAmount\", sortable: false },\n        { title: \"Salaire restant\", key: \"remainingSalary\", sortable: false },\n        { title: \"Actions\", key: \"actions\", sortable: false }\n      ],\n      months: [\n        { title: 'Janvier', value: 1 },\n        { title: 'Février', value: 2 },\n        { title: 'Mars', value: 3 },\n        { title: 'Avril', value: 4 },\n        { title: 'Mai', value: 5 },\n        { title: 'Juin', value: 6 },\n        { title: 'Juillet', value: 7 },\n        { title: 'Août', value: 8 },\n        { title: 'Septembre', value: 9 },\n        { title: 'Octobre', value: 10 },\n        { title: 'Novembre', value: 11 },\n        { title: 'Décembre', value: 12 }\n      ]\n    };\n  },\n  computed: {\n    ...mapGetters('salary', ['salaryData', 'loading', 'total', 'currentMonth', 'currentYear']),\n    ...mapGetters('agent', ['allAgents']),\n    \n    monthName() {\n      const month = this.months.find(m => m.value === this.selectedMonth);\n      return month ? month.title : '';\n    },\n    \n    years() {\n      const currentYear = new Date().getFullYear();\n      const years = [];\n      for (let i = currentYear - 2; i <= currentYear + 1; i++) {\n        years.push(i);\n      }\n      return years;\n    },\n    \n    agentOptions() {\n      return this.allAgents.map(agent => ({\n        title: agent.name,\n        value: agent.id\n      }));\n    }\n  },\n  \n  methods: {\n    async fetchData() {\n      const { page, itemsPerPage, sortBy } = this.options;\n      const sortKey = sortBy && sortBy.length > 0 ? sortBy[0].key : 'name';\n      const sortOrder = sortBy && sortBy.length > 0 ? sortBy[0].order : 'asc';\n\n      await this.$store.dispatch('salary/fetchSalaryData', {\n        userId: this.selectedAgent,\n        month: this.selectedMonth,\n        year: this.selectedYear,\n        page: page || 1,\n        limit: itemsPerPage || 10,\n        sortBy: sortKey,\n        order: sortOrder\n      });\n    },\n    \n    updateOptions(newOptions) {\n      this.options = newOptions;\n      this.fetchData();\n    },\n    \n    viewDetails(item) {\n      this.selectedItem = item;\n      this.detailsDialog = true;\n    },\n    \n    formatCurrency(amount) {\n      return new Intl.NumberFormat('fr-TN', {\n        style: 'currency',\n        currency: 'TND'\n      }).format(amount || 0);\n    },\n    \n    getRetardColor(minutes) {\n      if (minutes === 0) return 'green';\n      if (minutes < 30) return 'orange';\n      if (minutes < 60) return 'deep-orange';\n      return 'red';\n    },\n    \n    async exportToExcel() {\n      try {\n        const data = await this.$store.dispatch('salary/exportSalaryToExcel', {\n          userId: this.selectedAgent,\n          month: this.selectedMonth,\n          year: this.selectedYear\n        });\n\n        const worksheet = XLSX.utils.json_to_sheet(data.map(item => ({\n          'Agent': item.name,\n          'Email': item.email,\n          'Salaire de base': item.baseSalary,\n          'Retard total (minutes)': item.totalRetardMinutes,\n          'Retard total': item.totalRetardFormatted,\n          'Heures déduites': item.deductedHours,\n          'Montant déduit': item.deductedAmount,\n          'Salaire restant': item.remainingSalary,\n          'Mois': item.month,\n          'Année': item.year\n        })));\n\n        const workbook = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(workbook, worksheet, 'Calcul Salaires');\n        \n        const fileName = `calcul_salaires_${this.monthName}_${this.selectedYear}.xlsx`;\n        XLSX.writeFile(workbook, fileName);\n        \n        this.showSnackbar('Export Excel réussi', 'success');\n      } catch (error) {\n        console.error('Error exporting to Excel:', error);\n        this.showSnackbar('Erreur lors de l\\'export Excel', 'error');\n      }\n    },\n    \n    showSnackbar(text, color) {\n      this.snackbarText = text;\n      this.snackbarColor = color;\n      this.snackbar = true;\n    }\n  },\n  \n  async mounted() {\n    await this.$store.dispatch('agent/fetchAllAgents');\n    this.fetchData();\n  }\n};\n</script>\n\n<style scoped>\n.text-red {\n  color: #f44336 !important;\n}\n\n.text-green {\n  color: #4caf50 !important;\n}\n</style>\n"], "mappings": ";;;EAqFkBA,KAAK,EAAC;AAAc;;;;;;;;;;;;;;;;;;;;;;uBApFpCC,YAAA,CA+JcC,sBAAA;IAhKhBC,OAAA,EAAAC,QAAA,CAEI,MAiGS,CAjGTC,YAAA,CAiGSC,iBAAA;MAjGDC,SAAS,EAAC;IAAG;MAFzBJ,OAAA,EAAAC,QAAA,CAGM,MAEe,CAFfC,YAAA,CAEeG,uBAAA;QAFDR,KAAK,EAAC;MAAc;QAHxCG,OAAA,EAAAC,QAAA,CAGyC,MACX,CAJ9BK,gBAAA,CAGyC,yBACX,GAAAC,gBAAA,CAAGC,QAAA,CAAAC,SAAS,IAAG,GAAC,GAAAF,gBAAA,CAAGG,IAAA,CAAAC,WAAW,iB;QAJ5DC,CAAA;UAOMC,mBAAA,0BAA6B,EAC7BX,YAAA,CAqCQY,gBAAA;QArCDjB,KAAK,EAAC,MAAM;QAACkB,KAAK,EAAC;;QARhCf,OAAA,EAAAC,QAAA,CASQ,MASQ,CATRC,YAAA,CASQc,gBAAA;UATDC,IAAI,EAAC,IAAI;UAACC,EAAE,EAAC;;UAT5BlB,OAAA,EAAAC,QAAA,CAUU,MAOY,CAPZC,YAAA,CAOYiB,mBAAA;YAjBtBC,UAAA,EAWqBC,KAAA,CAAAC,aAAa;YAXlC,wB,oCAWqBD,KAAA,CAAAC,aAAa,GAAAC,MAAA,GAKDf,QAAA,CAAAgB,SAAS,C;YAJ7BC,KAAK,EAAEJ,KAAA,CAAAK,MAAM;YACdC,KAAK,EAAC,MAAM;YACZC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAC;;UAfpBjB,CAAA;YAmBQV,YAAA,CASQc,gBAAA;UATDC,IAAI,EAAC,IAAI;UAACC,EAAE,EAAC;;UAnB5BlB,OAAA,EAAAC,QAAA,CAoBU,MAOY,CAPZC,YAAA,CAOYiB,mBAAA;YA3BtBC,UAAA,EAqBqBC,KAAA,CAAAS,YAAY;YArBjC,wB,oCAqBqBT,KAAA,CAAAS,YAAY,GAAAP,MAAA,GAKAf,QAAA,CAAAgB,SAAS,C;YAJ7BC,KAAK,EAAEjB,QAAA,CAAAuB,KAAK;YACbJ,KAAK,EAAC,OAAO;YACbC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAC;;UAzBpBjB,CAAA;YA6BQV,YAAA,CAUQc,gBAAA;UAVDC,IAAI,EAAC,IAAI;UAACC,EAAE,EAAC;;UA7B5BlB,OAAA,EAAAC,QAAA,CA8BU,MAQY,CARZC,YAAA,CAQYiB,mBAAA;YAtCtBC,UAAA,EA+BqBC,KAAA,CAAAW,aAAa;YA/BlC,wB,oCA+BqBX,KAAA,CAAAW,aAAa,GAAAT,MAAA,GAMDf,QAAA,CAAAgB,SAAS,C;YAL7BC,KAAK,EAAEjB,QAAA,CAAAyB,YAAY;YACpBN,KAAK,EAAC,mBAAmB;YACzBC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAC,UAAU;YAClBK,SAAS,EAAT;;UApCZtB,CAAA;YAwCQV,YAAA,CAIQc,gBAAA;UAJDC,IAAI,EAAC,IAAI;UAACC,EAAE,EAAC;;UAxC5BlB,OAAA,EAAAC,QAAA,CAyCU,MAEQ,CAFRC,YAAA,CAEQiC,gBAAA;YAFAC,OAAK,EAAE5B,QAAA,CAAA6B,aAAa;YAAEC,KAAK,EAAC,OAAO;YAAC,cAAY,EAAC;;YAzCnEtC,OAAA,EAAAC,QAAA,CAyCoF,MAE1E,CA3CVK,gBAAA,CAyCoF,gBAE1E,E;YA3CVM,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UA+CMC,mBAAA,gBAAmB,EACnBX,YAAA,CAkDsBqC,8BAAA;QAjDnBC,OAAO,EAAEnB,KAAA,CAAAmB,OAAO;QAChBf,KAAK,EAAEf,IAAA,CAAA+B,UAAU;QACjB,cAAY,EAAE/B,IAAA,CAAAgC,KAAK;QACnBC,OAAO,EAAEjC,IAAA,CAAAiC,OAAO;QAChB,kBAAc,EAAEnC,QAAA,CAAAoC,aAAa;QAC9B/C,KAAK,EAAC;;QAEW,iBAAe,EAAAI,QAAA,CAC9B,CAAqC;UADH4C;QAAI,OAxDhDvC,gBAAA,CAAAC,gBAAA,CAyDaC,QAAA,CAAAsC,cAAc,CAACD,IAAI,CAACE,UAAU,kB;QAGlB,2BAAyB,EAAA9C,QAAA,CACxC,CAKS;UANmC4C;QAAI,OAChD3C,YAAA,CAKS8C,iBAAA;UAJNV,KAAK,EAAE9B,QAAA,CAAAyC,cAAc,CAACJ,IAAI,CAACK,kBAAkB;UAC9CC,IAAI,EAAC;;UA/DjBnD,OAAA,EAAAC,QAAA,CAiEY,MAA+B,CAjE3CK,gBAAA,CAAAC,gBAAA,CAiEesC,IAAI,CAACO,oBAAoB,iB;UAjExCxC,CAAA;;QAqEyB,oBAAkB,EAAAX,QAAA,CACjC,CAKS;UAN4B4C;QAAI,OACzC3C,YAAA,CAKS8C,iBAAA;UAJNV,KAAK,EAAEO,IAAI,CAACQ,aAAa;UAC1BF,IAAI,EAAC;;UAxEjBnD,OAAA,EAAAC,QAAA,CA0EY,MAAwB,CA1EpCK,gBAAA,CAAAC,gBAAA,CA0EesC,IAAI,CAACQ,aAAa,IAAG,IAC1B,gB;UA3EVzC,CAAA;;QA8EyB,qBAAmB,EAAAX,QAAA,CAClC,CAEO;UAH+B4C;QAAI,OAC1CS,mBAAA,CAEO;UAFAzD,KAAK,EA/EtB0D,eAAA,CA+EwBV,IAAI,CAACW,cAAc;4BAC5BhD,QAAA,CAAAsC,cAAc,CAACD,IAAI,CAACW,cAAc,yB;QAIxB,sBAAoB,EAAAvD,QAAA,CACnC,CAES;UAH8B4C;QAAI,OAC3CS,mBAAA,CAES,UAFTG,UAES,EAAAlD,gBAAA,CADJC,QAAA,CAAAsC,cAAc,CAACD,IAAI,CAACa,eAAe,kB;QAIzB,cAAY,EAAAzD,QAAA,CAC3B,CAKS;UANsB4C;QAAI,OACnC3C,YAAA,CAKSiC,gBAAA;UAJPwB,IAAI,EAAC,SAAS;UACdR,IAAI,EAAC,OAAO;UACXf,OAAK,EAAAb,MAAA,IAAEf,QAAA,CAAAoD,WAAW,CAACf,IAAI;UACxBhB,OAAO,EAAC;;QA/FpBjB,CAAA;;MAAAA,CAAA;QAqGIC,mBAAA,oBAAuB,EACvBX,YAAA,CAoDW2D,mBAAA;MA1JfzC,UAAA,EAsGuBC,KAAA,CAAAyC,aAAa;MAtGpC,uBAAAC,MAAA,QAAAA,MAAA,MAAAxC,MAAA,IAsGuBF,KAAA,CAAAyC,aAAa,GAAAvC,MAAA;MAAE,WAAS,EAAC;;MAtGhDvB,OAAA,EAAAC,QAAA,CAuGM,MAkDS,CAlDKoB,KAAA,CAAA2C,YAAY,I,cAA1BlE,YAAA,CAkDSK,iBAAA;QAzJf8D,GAAA;MAAA;QAAAjE,OAAA,EAAAC,QAAA,CAwGQ,MAEe,CAFfC,YAAA,CAEeG,uBAAA;UA1GvBL,OAAA,EAAAC,QAAA,CAwGsB,MACF,CAzGpBK,gBAAA,CAwGsB,aACF,GAAAC,gBAAA,CAAGc,KAAA,CAAA2C,YAAY,CAACE,IAAI,iB;UAzGxCtD,CAAA;YA2GQV,YAAA,CAuCciE,sBAAA;UAlJtBnE,OAAA,EAAAC,QAAA,CA4GU,MAqCQ,CArCRC,YAAA,CAqCQY,gBAAA;YAjJlBd,OAAA,EAAAC,QAAA,CA6GY,MAuBQ,CAvBRC,YAAA,CAuBQc,gBAAA;cAvBDC,IAAI,EAAC;YAAG;cA7G3BjB,OAAA,EAAAC,QAAA,CA8Gc,MAqBS,CArBTC,YAAA,CAqBSkE,iBAAA;gBAnIvBpE,OAAA,EAAAC,QAAA,CA+GgB,MAGc,CAHdC,YAAA,CAGcmE,sBAAA;kBAlH9BrE,OAAA,EAAAC,QAAA,CAgHkB,MAAuD,CAAvDC,YAAA,CAAuDoE,4BAAA;oBAhHzEtE,OAAA,EAAAC,QAAA,CAgHqC,MAAgB,CAhHrDK,gBAAA,CAgHqC,kBAAgB,E;oBAhHrDM,CAAA;sBAiHkBV,YAAA,CAA0FqE,+BAAA;oBAjH5GvE,OAAA,EAAAC,QAAA,CAiHwC,MAA6C,CAjHrFK,gBAAA,CAAAC,gBAAA,CAiH2CC,QAAA,CAAAsC,cAAc,CAACzB,KAAA,CAAA2C,YAAY,CAACjB,UAAU,kB;oBAjHjFnC,CAAA;;kBAAAA,CAAA;oBAmHgBV,YAAA,CAGcmE,sBAAA;kBAtH9BrE,OAAA,EAAAC,QAAA,CAoHkB,MAAoD,CAApDC,YAAA,CAAoDoE,4BAAA;oBApHtEtE,OAAA,EAAAC,QAAA,CAoHqC,MAAa,CApHlDK,gBAAA,CAoHqC,eAAa,E;oBApHlDM,CAAA;sBAqHkBV,YAAA,CAAoFqE,+BAAA;oBArHtGvE,OAAA,EAAAC,QAAA,CAqHwC,MAAuC,CArH/EK,gBAAA,CAAAC,gBAAA,CAqH2Cc,KAAA,CAAA2C,YAAY,CAACZ,oBAAoB,iB;oBArH5ExC,CAAA;;kBAAAA,CAAA;oBAuHgBV,YAAA,CAGcmE,sBAAA;kBA1H9BrE,OAAA,EAAAC,QAAA,CAwHkB,MAAuD,CAAvDC,YAAA,CAAuDoE,4BAAA;oBAxHzEtE,OAAA,EAAAC,QAAA,CAwHqC,MAAgB,CAxHrDK,gBAAA,CAwHqC,kBAAgB,E;oBAxHrDM,CAAA;sBAyHkBV,YAAA,CAA8EqE,+BAAA;oBAzHhGvE,OAAA,EAAAC,QAAA,CAyHwC,MAAgC,CAzHxEK,gBAAA,CAAAC,gBAAA,CAyH2Cc,KAAA,CAAA2C,YAAY,CAACX,aAAa,IAAG,GAAC,gB;oBAzHzEzC,CAAA;;kBAAAA,CAAA;oBA2HgBV,YAAA,CAGcmE,sBAAA;kBA9H9BrE,OAAA,EAAAC,QAAA,CA4HkB,MAAsD,CAAtDC,YAAA,CAAsDoE,4BAAA;oBA5HxEtE,OAAA,EAAAC,QAAA,CA4HqC,MAAe,CA5HpDK,gBAAA,CA4HqC,iBAAe,E;oBA5HpDM,CAAA;sBA6HkBV,YAAA,CAA+GqE,+BAAA;oBAAzF1E,KAAK,EAAC;kBAAU;oBA7HxDG,OAAA,EAAAC,QAAA,CA6HyD,MAAiD,CA7H1GK,gBAAA,CAAAC,gBAAA,CA6H4DC,QAAA,CAAAsC,cAAc,CAACzB,KAAA,CAAA2C,YAAY,CAACR,cAAc,kB;oBA7HtG5C,CAAA;;kBAAAA,CAAA;oBA+HgBV,YAAA,CAGcmE,sBAAA;kBAlI9BrE,OAAA,EAAAC,QAAA,CAgIkB,MAAuD,CAAvDC,YAAA,CAAuDoE,4BAAA;oBAhIzEtE,OAAA,EAAAC,QAAA,CAgIqC,MAAgB,CAhIrDK,gBAAA,CAgIqC,kBAAgB,E;oBAhIrDM,CAAA;sBAiIkBV,YAAA,CAAqIqE,+BAAA;oBAA/G1E,KAAK,EAAC;kBAA+B;oBAjI7EG,OAAA,EAAAC,QAAA,CAiI8E,MAAkD,CAjIhIK,gBAAA,CAAAC,gBAAA,CAiIiFC,QAAA,CAAAsC,cAAc,CAACzB,KAAA,CAAA2C,YAAY,CAACN,eAAe,kB;oBAjI5H9C,CAAA;;kBAAAA,CAAA;;gBAAAA,CAAA;;cAAAA,CAAA;gBAqIYV,YAAA,CAWQc,gBAAA;cAXDC,IAAI,EAAC;YAAG;cArI3BjB,OAAA,EAAAC,QAAA,CAsIc,MAA+D,CAA/DC,YAAA,CAA+DsE,0BAAA;gBAtI7ExE,OAAA,EAAAC,QAAA,CAsI+B,MAA4B,CAtI3DK,gBAAA,CAsI+B,8BAA4B,E;gBAtI3DM,CAAA;kBAuI4BS,KAAA,CAAA2C,YAAY,CAACS,YAAY,IAAIpD,KAAA,CAAA2C,YAAY,CAACS,YAAY,CAACC,MAAM,Q,cAA3E5E,YAAA,CAKSsE,iBAAA;gBA5IvBH,GAAA;cAAA;gBAAAjE,OAAA,EAAAC,QAAA,CAwI6B,MAAwC,E,kBAArD0E,mBAAA,CAGcC,SAAA,QA3I9BC,WAAA,CAwI2CxD,KAAA,CAAA2C,YAAY,CAACS,YAAY,EAAhCK,GAAG;uCAAvBhF,YAAA,CAGcuE,sBAAA;oBAHyCJ,GAAG,EAAEa,GAAG,CAACC;;oBAxIhF/E,OAAA,EAAAC,QAAA,CAyIkB,MAAqD,CAArDC,YAAA,CAAqDoE,4BAAA;sBAzIvEtE,OAAA,EAAAC,QAAA,CAyIqC,MAAc,CAzInDK,gBAAA,CAAAC,gBAAA,CAyIwCuE,GAAG,CAACC,IAAI,iB;sBAzIhDnE,CAAA;kDA0IkBV,YAAA,CAAsEqE,+BAAA;sBA1IxFvE,OAAA,EAAAC,QAAA,CA0IwC,MAAyB,CA1IjEK,gBAAA,CAAAC,gBAAA,CA0I2CuE,GAAG,CAACE,eAAe,iB;sBA1I9DpE,CAAA;;oBAAAA,CAAA;;;gBAAAA,CAAA;mCA6Icd,YAAA,CAEUmF,kBAAA;gBA/IxBhB,GAAA;gBA6I8BiB,IAAI,EAAC,SAAS;gBAACrD,OAAO,EAAC;;gBA7IrD7B,OAAA,EAAAC,QAAA,CA6I6D,MAE/C,CA/IdK,gBAAA,CA6I6D,2BAE/C,E;gBA/IdM,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;YAmJQV,YAAA,CAKiBiF,yBAAA;UAxJzBnF,OAAA,EAAAC,QAAA,CAoJU,MAAqB,CAArBC,YAAA,CAAqBkF,mBAAA,GACrBlF,YAAA,CAEQiC,gBAAA;YAFDG,KAAK,EAAC,eAAe;YAACT,OAAO,EAAC,MAAM;YAAEO,OAAK,EAAA2B,MAAA,QAAAA,MAAA,MAAAxC,MAAA,IAAEF,KAAA,CAAAyC,aAAa;;YArJ3E9D,OAAA,EAAAC,QAAA,CAqJqF,MAE3E,CAvJVK,gBAAA,CAqJqF,UAE3E,E;YAvJVM,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;YAAAC,mBAAA,e;MAAAD,CAAA;uCA4JIC,mBAAA,gCAAmC,EACnCX,YAAA,CAEamF,qBAAA;MA/JjBjE,UAAA,EA6JyBC,KAAA,CAAAiE,QAAQ;MA7JjC,uBAAAvB,MAAA,QAAAA,MAAA,MAAAxC,MAAA,IA6JyBF,KAAA,CAAAiE,QAAQ,GAAA/D,MAAA;MAAGe,KAAK,EAAEjB,KAAA,CAAAkE,aAAa;MAAEC,OAAO,EAAC;;MA7JlExF,OAAA,EAAAC,QAAA,CA8JM,MAAkB,CA9JxBK,gBAAA,CAAAC,gBAAA,CA8JSc,KAAA,CAAAoE,YAAY,iB;MA9JrB7E,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}