{"name": "backend", "version": "1.0.0", "description": "", "node_version": ">=18.18.2", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon index.js", "start": "node index.js", "test-cron": "node test-cron.js", "migrate": "node run-migration.js"}, "author": "", "license": "ISC", "dependencies": {"@faker-js/faker": "^9.0.2", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^3.5.0", "cros": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.19.2", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.45", "mysql2": "^3.10.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.13", "sequelize": "^6.37.3", "sequelize-cli": "^6.6.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.9"}}