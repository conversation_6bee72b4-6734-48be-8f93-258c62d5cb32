const express = require('express');
const router = express.Router();
const ProjectController = require('../controllers/projetController');
const { verifyToken, authorizeRoles } = require('../middleware/authMiddleware');

// GET all projects
router.get('/', verifyToken, authorizeRoles('admin'), ProjectController.getAllProjects);

// POST create a new project
router.post('/', verifyToken, authorize<PERSON><PERSON><PERSON>('admin'), ProjectController.createProject);

// PUT update a project
router.put('/:id', verifyToken, authorizeRoles('admin'), ProjectController.updateProject);

// DELETE delete a project
router.delete('/:id', verifyToken, authorizeRoles('admin'), ProjectController.deleteProject);

module.exports = router;