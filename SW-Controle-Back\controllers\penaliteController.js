const { Penalite, User, Schedule } = require('../models');
const moment = require('moment');
const { Op } = require('sequelize');

// Create a new penalite
exports.createPenalite = async (req, res) => {
  try {
    const { raison, startDate, endDate, ScheduleId, UserId } = req.body;

    // Validate required fields
    if (!UserId || !startDate || !endDate || !raison) {
      return res.status(400).json({ error: 'UserId, startDate, endDate, and raison are required' });
    }

    // Validate date range
    if (new Date(startDate) > new Date(endDate)) {
      return res.status(400).json({ error: 'Start date must be before or equal to end date' });
    }

    // Check for overlapping penalites for the same user
    const overlappingPenalite = await Penalite.findOne({
      where: {
        UserId: UserId,
        [Op.or]: [
          // New penalite starts within existing penalite
          {
            startDate: { [Op.lte]: startDate },
            endDate: { [Op.gte]: startDate }
          },
          // New penalite ends within existing penalite
          {
            startDate: { [Op.lte]: endDate },
            endDate: { [Op.gte]: endDate }
          },
          // New penalite completely contains existing penalite
          {
            startDate: { [Op.gte]: startDate },
            endDate: { [Op.lte]: endDate }
          }
        ]
      }
    });

    if (overlappingPenalite) {
      return res.status(400).json({
        error: 'Une pénalité existe déjà pour cette période ou chevauche avec cette période'
      });
    }

    // Calculate nbrDeJour
    const nbrDeJour = calculateWorkingDays(startDate, endDate);

    // Get the selected schedule
    const schedule = await Schedule.findOne({
      where: { isSelected: true },
      attributes: ['id'],
    });

    if (!schedule) {
      return res.status(400).json({ error: 'No active schedule found' });
    }

    const penalite = await Penalite.create({
      raison,
      startDate,
      endDate,
      nbrDeJour,
      UserId,
      ScheduleId: schedule.id
    });

    res.status(201).json(penalite);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
exports.getPenalites = async (req, res) => {
  try {
    const search = req.query.search || '';
    const page = parseInt(req.query.page, 10) || 1;
    let limit = parseInt(req.query.limit, 10) || 10;
    const sortBy = req.query.sortBy || 'name';
    const sortDesc = req.query.sortDesc === 'true';
    const agentId = req.query.agentId ? parseInt(req.query.agentId, 10) : null;
    const filterDate = req.query.date ? new Date(req.query.date) : null; // Step 1

    if (limit !== -1 && (page < 1 || limit < 1)) {
      return res.status(400).json({ message: 'Invalid pagination parameters' });
    }

    const whereClause = {
      role: 'employe',
      [Op.or]: [
        { name: { [Op.like]: `%${search}%` } },
      ],
    };

    if (agentId) {
      whereClause.id = agentId;
    }

    // Step 2: Modify wherePenalty based on filterDate
    let wherePenalty = {};

    if (filterDate) {
      wherePenalty = {
        startDate: {
          [Op.lte]: filterDate,
        },
        endDate: {
          [Op.gte]: filterDate,
        },
      };
    }

    // Step 3: Update include options
    const orderClause = getOrderBy(sortBy, sortDesc);

    const queryOptions = {
      attributes: ['id', 'name'],
      where: whereClause,
      include: [
        {
          model: Penalite,
          required: true,
          attributes: ['id', 'raison', 'startDate', 'endDate','nbrDeJour'],
          where: wherePenalty, // Apply date condition here
        },
      ],
      order: orderClause,
      distinct: true,
    };

    if (limit !== -1) {
      const offset = (page - 1) * limit;
      queryOptions.offset = offset;
      queryOptions.limit = limit;
    }

    const { count, rows } = await User.findAndCountAll(queryOptions);

    const result = rows.flatMap(user =>
      user.Penalites.map(penalite => {
        return {
          id: penalite.id,
          UserId: user.id,
          agent: user.name,
          startDate: penalite.startDate,
          endDate: penalite.endDate,
          nbrDeJour:penalite.nbrDeJour,
          raison: penalite.raison,
        };
      })
    );

    const totalItems = result.length;
    const totalPages = limit === -1 ? 1 : Math.ceil(count / limit);

    if (limit === -1) {
      limit = totalItems;
    }

    res.json({
      total: totalItems,
      totalPages,
      currentPage: limit === totalItems ? 1 : page,
      itemsPerPage: limit,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching penalites:', error);
    res.status(500).json({ message: 'Failed to fetch penalites', error: error.message });
  }
};

// Helper function to determine the order clause
function getOrderBy(sortBy, sortDesc) {
  if (sortBy === 'agent') {
    return [['name', sortDesc ? 'DESC' : 'ASC']];
  } else if (['startDate', 'endDate', 'raison'].includes(sortBy)) {
    return [[{ model: Penalite, as: 'Penalites' }, sortBy, sortDesc ? 'DESC' : 'ASC']];
  } else {
    return [['name', sortDesc ? 'DESC' : 'ASC']];
  }
}

// Update a penalite
exports.updatePenalite = async (req, res) => {
  try {
    const { id } = req.params;
    const { raison, startDate, endDate, ScheduleId, UserId } = req.body;

    const penalite = await Penalite.findByPk(id);

    if (!penalite) {
      return res.status(404).json({ error: 'Penalite not found' });
    }

    penalite.raison = raison || penalite.raison;
    penalite.startDate = startDate || penalite.startDate;
    penalite.endDate = endDate || penalite.endDate;
    penalite.ScheduleId = ScheduleId || penalite.ScheduleId;
    penalite.UserId = UserId || penalite.UserId;

    // Recalculate nbrDeJour if startDate or endDate changed
    if (startDate || endDate) {
      penalite.nbrDeJour = calculateWorkingDays(startDate,endDate);
    }

    await penalite.save();

    res.status(200).json(penalite);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Delete a penalite
exports.deletePenalite = async (req, res) => {
  try {
    const { id } = req.params;
 
    const penalite = await Penalite.findByPk(id);

    if (!penalite) {
      return res.status(404).json({ error: 'Penalite not found' });
    }

    await penalite.destroy();

    res.status(204).json();
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
function calculateWorkingDays(startDate, endDate) {
  let count = 0;
  let currentDate = moment(startDate);
  const end = moment(endDate);

  while (currentDate <= end) {
    if (currentDate.day() !== 0 && currentDate.day() !== 6) { // 0: Sunday, 6: Saturday
      count++;
    }
    currentDate.add(1, 'days');
  }

  return count;
}