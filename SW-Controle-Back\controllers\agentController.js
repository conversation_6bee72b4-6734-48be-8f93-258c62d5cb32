const db = require('../models');
const bcrypt = require('bcrypt');
const Agent = db.User;
const{UserInfo } = require('../models');
const { Op } = require('sequelize');
const nodemailer = require('nodemailer');

module.exports = {
  async getAgents(req, res) {
    try {
      const currentYear = new Date().getFullYear();
      const search = req.query.search || '';
      const page = parseInt(req.query.page, 10) || 1;
      const limit = parseInt(req.query.limit, 10) || 10;
      const sortBy = req.query.sortBy;
      const sortDesc = req.query.sortDesc === 'true';
  
      if (page < 1 || limit < 1) {
        return res.status(400).json({ message: 'Invalid pagination parameters' });
      }
  
      const offset = (page - 1) * limit;
  
      const whereClause = {
        role: 'employe',
        [Op.or]: [
          { name: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } },
          { phoneNumber: { [Op.like]: `%${search}%` } },
          { address: { [Op.like]: `%${search}%` } }
        ]
      };
  
      let orderClause;
      if (sortBy) {
        if (sortBy === 'UserInfo.months') {
          orderClause = [[{ model: db.UserInfo }, 'months', sortDesc ? 'DESC' : 'ASC']];
        } else if (sortBy === 'UserInfo.soldeAncienConge') {
          orderClause = [[{ model: db.UserInfo }, 'soldeAncienConge', sortDesc ? 'DESC' : 'ASC']];
        } else {
          orderClause = [[sortBy, sortDesc ? 'DESC' : 'ASC']];
        }
      } else {
        orderClause = [['name', 'ASC']];
      }
      const { count, rows } = await Agent.findAndCountAll({
        where: whereClause,
        limit: limit,
        offset: offset,
        order: orderClause,
        attributes: ['id', 'name', 'email', 'phoneNumber', 'address', 'salaire'],
        include: [{
          model: db.UserInfo,
          attributes: ['months', 'soldeAncienConge'],
          where: {
            year: currentYear
          }
        }]
      });
  
      const totalPages = Math.ceil(count / limit);
  
      res.json({
        total: count,
        totalPages: totalPages,
        currentPage: page,
        itemsPerPage: limit,
        agents: rows
      });
    } catch (error) {
      console.error('Error fetching agents:', error);
      res.status(500).json({ error: error.message });
    }
  },

  async createAgent(req, res) {
    try {
      const { name, email, password, months, role, phoneNumber, address, soldeAncienConge, salaire } = req.body;

      // Validate required fields
      if (!name || !email || !password || !phoneNumber || !address) {
        return res.status(400).json({
          message: 'Tous les champs obligatoires doivent être remplis (nom, email, mot de passe, téléphone, adresse)'
        });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          message: 'Format d\'email invalide'
        });
      }

      // Check if user with this email already exists
      const existingUser = await Agent.findOne({
        where: { email: email.toLowerCase() }
      });

      if (existingUser) {
        return res.status(400).json({
          message: 'Un utilisateur avec cette adresse email existe déjà'
        });
      }

      // Validate phone number format (8 digits)
      if (!/^\d{8}$/.test(phoneNumber)) {
        return res.status(400).json({
          message: 'Le numéro de téléphone doit contenir exactement 8 chiffres'
        });
      }

      // Validate password length
      if (password.length < 8) {
        return res.status(400).json({
          message: 'Le mot de passe doit contenir au moins 8 caractères'
        });
      }

      const hashedPassword = await bcrypt.hash(password, 12);
      const agent = await Agent.create({
        name,
        email: email.toLowerCase(), // Store email in lowercase for consistency
        password: hashedPassword,
        role: role || 'employe',
        phoneNumber,
        address,
        salaire
      });
      const currentYear = new Date().getFullYear();
      await UserInfo.create({
        UserId: agent.id,
        months: months,
        soldeAncienConge: soldeAncienConge,
        soldeConge: Math.round(months * 1.75),
        year: currentYear
      });
      const sendEmail=async (options)=>{
        const transporter = nodemailer.createTransport({
          host: 'smtp-relay.brevo.com',
          port: 587, 
          secure: false,
          auth: {
            user: '<EMAIL>',
            pass: 'xsmtpsib-93bab3885173d5225224cb4e51e43f1f9c53300ff1698fe8da15bf8d94de221d-vgCc7TFEjdw2fVSU'
          },
        });
        const emailOptions = {
          from: '<EMAIL>',
          to:options.email,
          subject: 'Création de votre compte - Informations de connexion',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
              <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h2 style="color: #2c3e50; text-align: center; margin-bottom: 30px;">Bienvenue dans SW Contrôle</h2>

                <p style="color: #34495e; font-size: 16px; line-height: 1.6;">Bonjour,</p>

                <p style="color: #34495e; font-size: 16px; line-height: 1.6;">
                  Un administrateur a créé votre compte sur notre plateforme. Voici vos informations de connexion :
                </p>

                <div style="background-color: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0;">
                  <p style="margin: 10px 0; color: #2c3e50;"><strong>Email :</strong> ${options.email}</p>
                  <p style="margin: 10px 0; color: #2c3e50;"><strong>Mot de passe :</strong> ${options.password}</p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                  <a href="https://rh.app-kamilex.com/"
                     style="background-color: #3498db; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; font-size: 16px;">
                    Se connecter à la plateforme
                  </a>
                </div>

                <p style="color: #34495e; font-size: 14px; line-height: 1.6;">
                  <strong>Important :</strong> Par mesure de sécurité, nous vous recommandons de modifier votre mot de passe dès votre première connexion.
                </p>

                <p style="color: #34495e; font-size: 14px; line-height: 1.6;">
                  Si vous n'avez pas demandé la création de ce compte ou si vous avez des questions, veuillez contacter l'équipe de support.
                </p>

                <hr style="border: none; border-top: 1px solid #ecf0f1; margin: 30px 0;">

                <p style="color: #7f8c8d; font-size: 12px; text-align: center;">
                  Cordialement,<br>
                  L'équipe SW Contrôle
                </p>
              </div>
            </div>
          `,
          text: `Bonjour,

      Un administrateur a créé votre compte sur notre plateforme. Voici vos informations de connexion :

      Email : ${options.email}
      Mot de passe : ${options.password}

      Lien de connexion : https://rh.app-kamilex.com/

      Par mesure de sécurité, nous vous recommandons de modifier votre mot de passe dès votre première connexion.

      Si vous n'avez pas demandé la création de ce compte ou si vous avez des questions, veuillez contacter l'équipe de support.

      Cordialement,
      L'équipe SW Contrôle
      `,
      };
       await transporter.sendMail(emailOptions)
     }
    await sendEmail({email:email,password:password})
  
      res.status(201).json({message:'done'});
    } catch (error) {
      console.error('Error creating agent:', error);
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error errors:', error.errors);

      // Handle database constraint violations
      if (error.name === 'SequelizeUniqueConstraintError' ||
          error.name === 'SequelizeUniqueConstraintError' ||
          (error.message && error.message.includes('Duplicate entry')) ||
          (error.message && error.message.includes('UNIQUE constraint failed')) ||
          (error.original && error.original.code === 'ER_DUP_ENTRY')) {
        return res.status(400).json({
          message: 'Un utilisateur avec cette adresse email existe déjà'
        });
      }

      // Handle validation errors
      if (error.name === 'SequelizeValidationError') {
        const validationErrors = error.errors.map(err => err.message);
        return res.status(400).json({
          message: 'Erreur de validation: ' + validationErrors.join(', ')
        });
      }

      // Handle other database errors that might indicate duplicates
      if (error.message && error.message.toLowerCase().includes('duplicate')) {
        return res.status(400).json({
          message: 'Un utilisateur avec cette adresse email existe déjà'
        });
      }

      res.status(500).json({
        message: 'Erreur lors de la création de l\'utilisateur'
      });
    }
  },

  async updateAgent(req, res) {
    try {
      const { id } = req.params;
      const { name, email, phoneNumber, address, userInfo, salaire } = req.body;

      const user = await Agent.findByPk(id);

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Check if email is being changed and if it already exists
      if (email && email.toLowerCase() !== user.email.toLowerCase()) {
        const existingUser = await Agent.findOne({
          where: {
            email: email.toLowerCase(),
            id: { [require('sequelize').Op.ne]: id } // Exclude current user
          }
        });

        if (existingUser) {
          return res.status(400).json({
            message: 'Un utilisateur avec cette adresse email existe déjà'
          });
        }
      }

      // Validate email format if provided
      if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          return res.status(400).json({
            message: 'Format d\'email invalide'
          });
        }
      }

      // Validate phone number format if provided
      if (phoneNumber && !/^\d{8}$/.test(phoneNumber)) {
        return res.status(400).json({
          message: 'Le numéro de téléphone doit contenir exactement 8 chiffres'
        });
      }

      const updatedFields = {
        name,
        email: email ? email.toLowerCase() : user.email,
        phoneNumber,
        address,
        salaire
      };

      await user.update(updatedFields);
  
      const currentYear = new Date().getFullYear();
  
      // Check if UserInfo entry for the current year exists
      const existingUserInfo = await UserInfo.findOne({
        where: {
          UserId: user.id,
          year: currentYear
        }
      });
  
      if (existingUserInfo) {
        // Update existing UserInfo entry
        await existingUserInfo.update({
          months: userInfo.months || 0,
          soldeAncienConge: userInfo.soldeAncienConge || 0
        });
      } else {
        // Create a new UserInfo entry
        await UserInfo.create({
          UserId: user.id,
          year: currentYear,
          months: userInfo.months || 0,
          soldeAncienConge: userInfo.soldeAncienConge || 0,
          congePrise: 0,
          sanctions: 0,
          resteConge: 0,
          previousTotalUsage: 0,
          isSoldeAncienCongeManual: false,
          lastCalculatedAt: null
        });
      }
  
      res.json({ message: 'Agent updated successfully.' });
    } catch (error) {
      console.error('Error updating agent:', error);

      // Handle database constraint violations
      if (error.name === 'SequelizeUniqueConstraintError') {
        if (error.errors && error.errors[0] && error.errors[0].path === 'email') {
          return res.status(400).json({
            message: 'Un utilisateur avec cette adresse email existe déjà'
          });
        }
      }

      // Handle validation errors
      if (error.name === 'SequelizeValidationError') {
        const validationErrors = error.errors.map(err => err.message);
        return res.status(400).json({
          message: 'Erreur de validation: ' + validationErrors.join(', ')
        });
      }

      res.status(500).json({
        message: 'Erreur lors de la mise à jour de l\'utilisateur'
      });
    }
  },
  async deleteAgent(req, res) {
    try {
      const { id } = req.params;
      const agent = await Agent.findByPk(id);
      
      if (!agent) {
        return res.status(404).json({ message: 'Agent not found' });
      }

      await agent.destroy();
      res.json({ message: 'Agent deleted successfully' });
    } catch (error) {
      console.error('Error deleting agent:', error);
      res.status(500).json({ message: error});
    }
  },
  async AgentInfo(req, res) {
    try {
      const { userId } = req.query;
      const userDetails = await Agent.findOne({
        where: { id: userId },
        attributes: [
          'name',
          'email',
          'address',
          'phoneNumber'
        ],
        include: [{
          model: UserInfo,
          attributes: [
            'months',
            'soldeAncienConge'
          ],
        }],
        raw: true,
        nest: true
      });
  
      // Check if user is found
      if (!userDetails) {
        return res.status(404).json({ message: 'User not found' });
      }
  
      // Format the response
      const user = {
        name: userDetails.name,
        email: userDetails.email,
        address: userDetails.address,
        phoneNumber: userDetails.phoneNumber,
        months: userDetails.UserInfo?.months || null,
        soldeAncienConge: userDetails.UserInfo?.soldeAncienConge ||0
      };
  
      // Return the response as JSON
      return res.status(200).json(user);
    } catch (error) {
      console.error('Error fetching user details:', error);
      return res.status(500).json({ message: 'An error occurred while fetching user details', error: error.message });
    }
  },
  async getEmployeUsers(req, res) {
    try {
      const users = await Agent.findAll({
        attributes: ['id', 'name'],
        where: {
          role: 'employe'
        },
        raw: true
      });
      return res.status(200).json(users);
    } catch (error) {
      console.error('Error fetching employe users:', error);
      
      // Error response
      return res.status(500).json({
        success: false,
        message: 'Unable to retrieve employe users',
        error: error.message
      });
    }
  }

};