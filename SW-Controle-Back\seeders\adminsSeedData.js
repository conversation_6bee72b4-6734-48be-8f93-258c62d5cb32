const { Sequelize } = require('sequelize');
const bcrypt = require('bcrypt');
require('dotenv').config();

const sequelize = new Sequelize('test','root','pass', {
  host: 'localhost',
  dialect: 'mysql',
});

// Seed data for the Users table
const saltRounds = 12;
const password = 'swcontrole';

const userSeedData = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    email: 'sa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@swadmin.com',
    role: 'admin',
    password: bcrypt.hashSync(password, saltRounds), // Encrypt password
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    password: bcrypt.hashSync(password, saltRounds), // Encrypt password
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    email: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@swadmin.com',
    role: 'admin',
    password: bcrypt.hashSync(password, saltRounds), // Encrypt password
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Function to insert seed data
async function insertAdminUsers() {
  try {
    await sequelize.authenticate();
    console.log('Database connected...');

    // Insert seed data into the Users table
    await sequelize.getQueryInterface().bulkInsert('Users', userSeedData);
    console.log('Admin user records inserted successfully!');
  } catch (error) {
    console.error('Error inserting admin user records:', error);
  } finally {
    await sequelize.close();
  }
}

// Execute the seeder
insertAdminUsers();
