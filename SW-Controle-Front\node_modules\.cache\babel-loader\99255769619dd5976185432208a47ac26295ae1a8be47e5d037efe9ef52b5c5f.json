{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport store from '@/store';\nimport Login from '../views/Login.vue';\nimport Layout from '../views/Layout.vue';\nimport VueGlobalPresence from '../views/VueGlobalPresence.vue';\nimport Home from '../views/Home.vue';\nimport ForgotPassword from '../views/ForgotPassword.vue';\nimport ResetPassword from '../views/ResetPassword.vue';\nimport ForgetPassSucc from '../views/ForgetPassSucc.vue';\nimport ResetPassSucc from '../views/ResetPassSucc.vue';\nimport DemandeConge from '../views/DemandeConge.vue';\nimport Profile from '../views/Profile.vue';\nimport vuepresenceparticulier from '../views/VuePresenceParticulier.vue';\nimport gestionutilisateur from '../views/GestionUtilisateur.vue';\nimport demandeautorisation from '../views/DemandeAutorisation.vue';\nimport presence from '../views/presence.vue';\nimport penalite from '../views/penalite.vue';\nimport absence from '../views/absence.vue';\nimport retard from '../views/retard.vue';\nimport retardparticulier from '@/views/employe/retardparticulier.vue';\nimport SalaryCalculation from '../views/SalaryCalculation.vue';\nimport Homeemployee from '../views/employe/Home.vue';\nimport demandeautorisationemp from '../views/employe/demadeautorisation.vue';\nimport demandecongeemp from '../views/employe/demandeconge.vue';\nimport projet from '../views/Projet.vue';\nimport ConfigHoraire from '../views/ConfigHoraire.vue';\nimport DemandeAutorisation from '../views/DemandeAutorisation.vue';\nimport conge from '@/views/employe/conge.vue';\nimport pointage from '@/views/employe/pointage.vue';\nimport Confirmationpresence from '@/views/confirmationpresence.vue';\nconst routes = [{\n  path: '/',\n  name: 'Login',\n  component: Login\n}, {\n  path: '/forgot-password',\n  name: 'ForgotPassword',\n  component: ForgotPassword\n}, {\n  path: '/reset-password/:token',\n  name: 'ResetPassword',\n  component: ResetPassword\n}, {\n  path: '/forgot-password-success',\n  name: 'ForgotPasswordSuccess',\n  component: ForgetPassSucc\n}, {\n  path: '/reset-password-success',\n  name: 'ResetPasswordSuccess',\n  component: ResetPassSucc\n}, {\n  path: '/app',\n  component: Layout,\n  meta: {\n    requiresAuth: true,\n    role: 'admin'\n  },\n  children: [{\n    path: 'Home',\n    name: 'Home Admin',\n    component: Home\n  }, {\n    path: 'conge',\n    name: 'Congé',\n    component: VueGlobalPresence\n  }, {\n    path: 'demandeconge',\n    name: 'Demande Congé',\n    component: DemandeConge\n  }, {\n    path: 'presenceparticulier/:id',\n    name: 'synthèse congé particulier',\n    component: vuepresenceparticulier\n  }, {\n    path: 'gestionutilisateur',\n    name: 'Gestion Utilisateur',\n    component: gestionutilisateur\n  }, {\n    path: 'demandeautorisation',\n    name: 'Demande Autorisation',\n    component: DemandeAutorisation\n  }, {\n    path: 'presence',\n    name: 'Présence',\n    component: presence\n  }, {\n    path: 'penalite',\n    name: 'Penalité',\n    component: penalite\n  }, {\n    path: 'absence',\n    name: 'Absence',\n    component: absence\n  }, {\n    path: 'retard',\n    name: 'Retard',\n    component: retard\n  }, {\n    path: 'projets',\n    name: 'Projets',\n    component: projet\n  }, {\n    path: 'confighoraire',\n    name: \"configuration d'horaire\",\n    component: ConfigHoraire\n  }, {\n    path: 'confirmationpresence',\n    name: 'Pointage Globale',\n    component: Confirmationpresence\n  }]\n}, {\n  path: '/employee',\n  component: Layout,\n  meta: {\n    requiresAuth: true,\n    role: 'employe'\n  },\n  children: [{\n    path: 'home',\n    name: 'Home',\n    component: Homeemployee\n  }, {\n    path: 'presenceparticulier',\n    name: 'Presence particulier employé',\n    component: conge\n  }, {\n    path: 'demandeautorisation',\n    name: 'Demande autorisation employé',\n    component: demandeautorisationemp\n  }, {\n    path: 'demandeconge',\n    name: 'Demande congé employé',\n    component: demandecongeemp\n  }, {\n    path: 'retardp',\n    name: 'Retard Particulier',\n    component: retardparticulier\n  }, {\n    path: 'pointage',\n    name: 'pointage',\n    component: pointage\n  }, {\n    path: 'profile',\n    name: 'Profile',\n    component: Profile\n  }]\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\n\n// Authentication guard\nrouter.beforeEach((to, from, next) => {\n  const isLoggedIn = !!localStorage.getItem('accessToken');\n  const userRole = store.getters['auth/userRole'];\n  const requiredRole = to.meta.role;\n  if (to.meta.requiresAuth) {\n    if (!isLoggedIn) {\n      next({\n        name: 'Login'\n      });\n    } else if (requiredRole && userRole !== requiredRole) {\n      // Redirect to appropriate route based on role\n      const redirectRoute = userRole === 'admin' ? 'Congé' : 'pointage';\n      next({\n        name: redirectRoute\n      });\n    } else {\n      next();\n    }\n  } else {\n    if (isLoggedIn) {\n      // If user is logged in and trying to access auth pages, redirect to appropriate route\n      const redirectRoute = userRole === 'admin' ? 'Congé' : 'pointage';\n      next({\n        name: redirectRoute\n      });\n    } else {\n      next();\n    }\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "store", "<PERSON><PERSON>", "Layout", "VueGlobalPresence", "Home", "ForgotPassword", "ResetPassword", "ForgetPassSucc", "ResetPassSucc", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Profile", "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gestionutilisateur", "demandeautorisation", "presence", "penalite", "absence", "retard", "retard<PERSON><PERSON><PERSON><PERSON>", "SalaryCalculation", "Homeemployee", "demandeautorisationemp", "demandecongeemp", "projet", "ConfigHoraire", "DemandeAutorisation", "conge", "pointage", "Confirmationpresence", "routes", "path", "name", "component", "meta", "requiresAuth", "role", "children", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "isLoggedIn", "localStorage", "getItem", "userRole", "getters", "requiredRole", "redirectRoute"], "sources": ["C:/Users/<USER>/Desktop/swcontrole/SW-Controle-Front/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport store from '@/store' \r\nimport Login from '../views/Login.vue'\r\nimport Layout from '../views/Layout.vue'\r\nimport VueGlobalPresence from '../views/VueGlobalPresence.vue'\r\nimport Home from '../views/Home.vue'\r\nimport ForgotPassword from '../views/ForgotPassword.vue'\r\nimport ResetPassword from '../views/ResetPassword.vue'\r\nimport ForgetPassSucc from '../views/ForgetPassSucc.vue'\r\nimport ResetPassSucc from '../views/ResetPassSucc.vue'\r\nimport DemandeConge from '../views/DemandeConge.vue'\r\nimport Profile from '../views/Profile.vue'\r\nimport vuepresenceparticulier from '../views/VuePresenceParticulier.vue'\r\nimport gestionutilisateur from '../views/GestionUtilisateur.vue'\r\nimport demandeautorisation from '../views/DemandeAutorisation.vue'\r\nimport presence from '../views/presence.vue'\r\nimport penalite from '../views/penalite.vue'\r\nimport absence from '../views/absence.vue'\r\nimport retard from '../views/retard.vue'\r\nimport retardparticulier from '@/views/employe/retardparticulier.vue'\r\nimport SalaryCalculation from '../views/SalaryCalculation.vue'\r\nimport Homeemployee from '../views/employe/Home.vue'\r\nimport demandeautorisationemp from '../views/employe/demadeautorisation.vue'\r\nimport demandecongeemp from '../views/employe/demandeconge.vue'\r\nimport projet from '../views/Projet.vue'\r\nimport ConfigHoraire from '../views/ConfigHoraire.vue'\r\nimport DemandeAutorisation from '../views/DemandeAutorisation.vue'\r\nimport conge from '@/views/employe/conge.vue'\r\nimport pointage from '@/views/employe/pointage.vue'\r\nimport Confirmationpresence from '@/views/confirmationpresence.vue'\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'Login',\r\n    component: Login\r\n  },\r\n  {\r\n    path: '/forgot-password',\r\n    name: 'ForgotPassword',\r\n    component: ForgotPassword\r\n  },\r\n  {\r\n    path: '/reset-password/:token',\r\n    name: 'ResetPassword',\r\n    component: ResetPassword\r\n  },\r\n  {\r\n    path: '/forgot-password-success',\r\n    name: 'ForgotPasswordSuccess',\r\n    component: ForgetPassSucc\r\n  },\r\n  {\r\n    path: '/reset-password-success',\r\n    name: 'ResetPasswordSuccess',\r\n    component: ResetPassSucc\r\n  },\r\n  {\r\n    path: '/app',\r\n    component: Layout,\r\n    meta: { requiresAuth: true, role: 'admin' },\r\n    children: [\r\n      {\r\n        path: 'Home',\r\n        name: 'Home Admin',\r\n        component: Home\r\n      },\r\n     \r\n      {\r\n        path: 'conge',\r\n        name: 'Congé',\r\n        component: VueGlobalPresence\r\n      },\r\n      {\r\n        path: 'demandeconge',\r\n        name: 'Demande Congé',\r\n        component: DemandeConge\r\n      },\r\n      {\r\n        path: 'presenceparticulier/:id',\r\n        name: 'synthèse congé particulier',\r\n        component: vuepresenceparticulier\r\n      },\r\n      {\r\n        path: 'gestionutilisateur',\r\n        name: 'Gestion Utilisateur',\r\n        component: gestionutilisateur\r\n      },\r\n      {\r\n        path: 'demandeautorisation',\r\n        name: 'Demande Autorisation',\r\n        component: DemandeAutorisation\r\n      },\r\n      {\r\n        path: 'presence',\r\n        name: 'Présence',\r\n        component: presence\r\n      },\r\n      {\r\n        path: 'penalite',\r\n        name: 'Penalité',\r\n        component: penalite\r\n      },\r\n      {\r\n        path: 'absence',\r\n        name: 'Absence',\r\n        component: absence\r\n      },\r\n      {\r\n        path: 'retard',\r\n        name: 'Retard',\r\n        component: retard\r\n      },\r\n    \r\n      {\r\n        path: 'projets',\r\n        name: 'Projets',\r\n        component: projet\r\n      },\r\n      {\r\n        path: 'confighoraire',\r\n        name: \"configuration d'horaire\",\r\n        component: ConfigHoraire\r\n      },\r\n      {\r\n        path: 'confirmationpresence',\r\n        name: 'Pointage Globale',\r\n        component:Confirmationpresence\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/employee',\r\n    component: Layout,\r\n    meta: { requiresAuth: true, role: 'employe' },\r\n    children: [\r\n      {\r\n        path: 'home',\r\n        name: 'Home',\r\n        component: Homeemployee\r\n      },\r\n      {\r\n        path: 'presenceparticulier',\r\n        name: 'Presence particulier employé',\r\n        component:conge,\r\n      },\r\n      {\r\n        path: 'demandeautorisation',\r\n        name: 'Demande autorisation employé',\r\n        component: demandeautorisationemp\r\n      },\r\n      {\r\n        path: 'demandeconge',\r\n        name: 'Demande congé employé',\r\n        component: demandecongeemp\r\n      },\r\n      {\r\n        path: 'retardp',\r\n        name: 'Retard Particulier',\r\n        component: retardparticulier\r\n      },\r\n      {\r\n        path: 'pointage',\r\n        name: 'pointage',\r\n        component: pointage\r\n      },\r\n      {\r\n        path: 'profile',\r\n        name: 'Profile',\r\n        component: Profile\r\n      },\r\n    \r\n      \r\n    ]\r\n  }\r\n]\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes\r\n})\r\n\r\n// Authentication guard\r\nrouter.beforeEach((to, from, next) => {\r\n  const isLoggedIn = !!localStorage.getItem('accessToken');\r\n  const userRole = store.getters['auth/userRole'];\r\n  const requiredRole = to.meta.role;\r\n\r\n  if (to.meta.requiresAuth) {\r\n    if (!isLoggedIn) {\r\n      next({ name: 'Login' });\r\n    } else if (requiredRole && userRole !== requiredRole) {\r\n      // Redirect to appropriate route based on role\r\n      const redirectRoute = userRole === 'admin' ? 'Congé' : 'pointage';\r\n      next({ name: redirectRoute });\r\n    } else {\r\n      next();\r\n    }\r\n  } else {\r\n    if (isLoggedIn) {\r\n      // If user is logged in and trying to access auth pages, redirect to appropriate route\r\n      const redirectRoute = userRole === 'admin' ? 'Congé' : 'pointage';\r\n      next({ name: redirectRoute });\r\n    } else {\r\n      next();\r\n    }\r\n  }\r\n});\r\n\r\nexport default router"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE/B;AACb,CAAC,EACD;EACE6B,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAE3B;AACb,CAAC,EACD;EACEyB,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAE1B;AACb,CAAC,EACD;EACEwB,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEzB;AACb,CAAC,EACD;EACEuB,IAAI,EAAE,yBAAyB;EAC/BC,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,EAAExB;AACb,CAAC,EACD;EACEsB,IAAI,EAAE,MAAM;EACZE,SAAS,EAAE9B,MAAM;EACjB+B,IAAI,EAAE;IAAEC,YAAY,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAQ,CAAC;EAC3CC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE5B;EACb,CAAC,EAED;IACE0B,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE7B;EACb,CAAC,EACD;IACE2B,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEvB;EACb,CAAC,EACD;IACEqB,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,4BAA4B;IAClCC,SAAS,EAAErB;EACb,CAAC,EACD;IACEmB,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAEpB;EACb,CAAC,EACD;IACEkB,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAEP;EACb,CAAC,EACD;IACEK,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAElB;EACb,CAAC,EACD;IACEgB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEjB;EACb,CAAC,EACD;IACEe,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEhB;EACb,CAAC,EACD;IACEc,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEf;EACb,CAAC,EAED;IACEa,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAET;EACb,CAAC,EACD;IACEO,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,yBAAyB;IAC/BC,SAAS,EAAER;EACb,CAAC,EACD;IACEM,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAACJ;EACZ,CAAC;AAEL,CAAC,EACD;EACEE,IAAI,EAAE,WAAW;EACjBE,SAAS,EAAE9B,MAAM;EACjB+B,IAAI,EAAE;IAAEC,YAAY,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAU,CAAC;EAC7CC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEZ;EACb,CAAC,EACD;IACEU,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,8BAA8B;IACpCC,SAAS,EAACN;EACZ,CAAC,EACD;IACEI,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,8BAA8B;IACpCC,SAAS,EAAEX;EACb,CAAC,EACD;IACES,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAEV;EACb,CAAC,EACD;IACEQ,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAEd;EACb,CAAC,EACD;IACEY,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEL;EACb,CAAC,EACD;IACEG,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEtB;EACb,CAAC;AAIL,CAAC,CACF;AAED,MAAM2B,MAAM,GAAGvC,YAAY,CAAC;EAC1BwC,OAAO,EAAEvC,gBAAgB,CAACwC,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CZ;AACF,CAAC,CAAC;;AAEF;AACAQ,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,MAAMC,UAAU,GAAG,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EACxD,MAAMC,QAAQ,GAAGjD,KAAK,CAACkD,OAAO,CAAC,eAAe,CAAC;EAC/C,MAAMC,YAAY,GAAGR,EAAE,CAACV,IAAI,CAACE,IAAI;EAEjC,IAAIQ,EAAE,CAACV,IAAI,CAACC,YAAY,EAAE;IACxB,IAAI,CAACY,UAAU,EAAE;MACfD,IAAI,CAAC;QAAEd,IAAI,EAAE;MAAQ,CAAC,CAAC;IACzB,CAAC,MAAM,IAAIoB,YAAY,IAAIF,QAAQ,KAAKE,YAAY,EAAE;MACpD;MACA,MAAMC,aAAa,GAAGH,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,UAAU;MACjEJ,IAAI,CAAC;QAAEd,IAAI,EAAEqB;MAAc,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLP,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IACL,IAAIC,UAAU,EAAE;MACd;MACA,MAAMM,aAAa,GAAGH,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,UAAU;MACjEJ,IAAI,CAAC;QAAEd,IAAI,EAAEqB;MAAc,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLP,IAAI,CAAC,CAAC;IACR;EACF;AACF,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}