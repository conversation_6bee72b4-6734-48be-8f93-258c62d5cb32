// models/presence.js
module.exports = (sequelize, DataTypes) => {
  const Presence = sequelize.define('Presence', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false
    },
    environnement: {
      type: DataTypes.STRING,
      allowNull: true
    },
    entree: {
      type: DataTypes.TIME,
      allowNull: true
    },
    sortie: {
      type: DataTypes.TIME,
      allowNull: true
    },
    entree1: {
      type: DataTypes.TIME,
      allowNull: true
    },
    sortie1: {
      type: DataTypes.TIME,
      allowNull: true
    },
    prod: {
      type: DataTypes.INTEGER,
      allowNull: true
  },
  prodMatin: {
      type: DataTypes.INTEGER,
      allowNull: true
  },
  prodApresMidi: {
      type: DataTypes.INTEGER,
      allowNull: true
  },
    retardm: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    retardam: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    retardtotal: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    commentaires: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    UserId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    ScheduleId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Schedules',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'NO ACTION'
    },
    // New confirmation status fields
    morningEntryStatus: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    morningExitStatus: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    afternoonEntryStatus: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    afternoonExitStatus: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    overallStatus: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    // Tallying modification tracking fields
    entreeModified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    sortieModified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    entree1Modified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    sortie1Modified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    }
  }, {
    tableName: 'presences',
    charset: 'utf8mb4',
    collate: 'utf8mb4_general_ci'
  });

  Presence.associate = function(models) {
    Presence.belongsTo(models.User, {
      foreignKey: 'UserId',
      onDelete: 'NO ACTION',
      onUpdate: 'CASCADE'
    });
    Presence.belongsTo(models.Schedule, {
      foreignKey: 'ScheduleId',
      onDelete: 'NO ACTION',
      onUpdate: 'CASCADE'
    });
  };

  return Presence;
};
