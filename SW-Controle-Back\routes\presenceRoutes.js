const express = require('express');
const router = express.Router();
const presenceController = require('../controllers/presenceController');
const { verifyToken, authorizeRoles } = require('../middleware/authMiddleware');

// GET presence and absence data
router.get('/presence/fetch', verifyToken, authorizeR<PERSON>s('admin'), presenceController.getPresenceAndAbsence);

// POST add pointage
router.post('/presence/addpointage', verifyToken, authorizeRoles('employe'), presenceController.addPointage);

// GET check button status
router.get('/presence/checkButtonStatus', verifyToken, authorize<PERSON><PERSON>s('employe'), presenceController.checkButtonStatus);

// PUT update presence times
router.put('/presence/addtimes', verifyToken, authorizeRoles('employe'), presenceController.updatePresence);

// GET all presences
router.get('/presence/getPresences', verifyToken, authorizeRoles('admin'), presenceController.getPresences);

// POST update a specific presence field
router.post('/presence/update-presence-field', verifyToken, authorizeR<PERSON>s('admin'), presenceController.updatePresenceField);

// POST update all statuses
router.post('/presence/update-all-statuses', verifyToken, authorizeRoles('admin'), presenceController.updateAllStatuses);

// GET check if the user has a leave (congé) today
router.get('/presence/conge/today', verifyToken, authorizeRoles('employe'), presenceController.checkCongeToday);

// GET check if the user has a penalty (pénalité) today
router.get('/presence/penalite/today', verifyToken, authorizeRoles('employe'), presenceController.checkPenaliteToday);

// GET current presence status
router.get('/presence/current-status', verifyToken, authorizeRoles('employe'), presenceController.getCurrentPresenceStatus);
router.get('/presence/getCurrentDateTime', verifyToken, authorizeRoles('employe'), presenceController.getCurrentDateTime);
module.exports = router;
