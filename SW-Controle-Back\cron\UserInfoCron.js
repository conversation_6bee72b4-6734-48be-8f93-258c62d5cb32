// cronjobs.js
const { User, UserInfo } = require('../models');
const { Sequelize } = require('sequelize');

const createNewUserInfoEntries = async () => {
  try {
    const currentYear = new Date().getFullYear();
    const users = await User.findAll();

    for (const user of users) {
      const existingUserInfo = await UserInfo.findOne({
        where: {
          UserId: user.id,
          year: currentYear
        }
      });

      if (!existingUserInfo) {
        await UserInfo.create({
          UserId: user.id,
          year: currentYear,
          months: 0,
          soldeAncienConge: 0,
          congePrise: 0,
          sanctions: 0,
          resteConge: 0,
          previousTotalUsage: 0,
          isSoldeAncienCongeManual: false,
          lastCalculatedAt: null
        });
      }
    }

    console.log(`New UserInfo entries for ${currentYear} created successfully.`);
  } catch (error) {
    console.error('Error creating new UserInfo entries:', error);
  }
};

module.exports = { createNewUserInfoEntries };