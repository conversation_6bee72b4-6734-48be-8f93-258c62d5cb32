const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
router.post('/login', authController.login);
router.patch('/forgotPassword', authController.forgotPassword); 
router.put('/resetPassword/:token', authController.resetPassword);
router.post("/logout",authController.logout);
router.put('/resetpasswordprofile', authController.resetPasswordProfile);
module.exports = router;