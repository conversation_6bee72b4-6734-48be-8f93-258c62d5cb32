const { Sequelize, DataTypes } = require('sequelize');
require('dotenv').config();
const sequelize = new Sequelize('test','root','pass', {
  host:'localhost',
  dialect: 'mysql',
});
// Seed data for the Schedule table
const seedData = [
  {
    id: 1,
    name: 'HORAIRE NORMALE',
    morningStart: '09:00:00',
    morningEnd: '13:00:00',
    breakStart: '13:00:00',
    breakEnd: '14:00:00',
    afternoonStart: '14:00:00',
    afternoonEnd: '18:00:00',
    isRecurring: true,
    isSelected: true,
    startDate: null,
    endDate: null,
    createdAt: '2024-10-23 13:49:50',
    updatedAt: '2025-01-17 09:15:42',
  },
  {
    id: 3,
    name: 'HORAIRE RAMADAN',
    morningStart: '08:00:00',
    morningEnd: '15:30:00',
    breakStart: '00:00:00',
    breakEnd: '00:00:00',
    afternoonStart: '00:00:00',
    afternoonEnd: '00:00:00',
    isRecurring: false,
    isSelected: false,
    startDate: '2025-03-11',
    endDate: '2025-04-08',
    createdAt: '2024-10-23 14:48:29',
    updatedAt: '2025-01-17 09:15:42',
  },
  {
    id: 4,
    name: 'HORAIRE D\'ETE',
    morningStart: '08:00:00',
    morningEnd: '16:30:00',
    breakStart: '00:00:00',
    breakEnd: '00:00:00',
    afternoonStart: '00:00:00',
    afternoonEnd: '00:00:00',
    isRecurring: false,
    isSelected: false,
    startDate: '2025-06-01',
    endDate: '2025-12-13',
    createdAt: '2024-12-13 10:01:51',
    updatedAt: '2025-01-17 09:15:42',
  },
];

// Function to insert seed data
async function insertSeedData() {
  try {
    await sequelize.authenticate();
    console.log('Database connected...');

    // Insert seed data into the Schedules table
    await sequelize.getQueryInterface().bulkInsert('Schedules', seedData);
    console.log('Schedule records inserted successfully!');
  } catch (error) {
    console.error('Error inserting schedule records:', error);
  } finally {
    await sequelize.close();
  }
}

// Execute the seeder
insertSeedData();
