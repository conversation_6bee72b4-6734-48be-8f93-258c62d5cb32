{"ast": null, "code": "import axios from 'axios';\nconst getAccessToken = () => localStorage.getItem('accessToken');\nconst state = {\n  salaryData: [],\n  loading: false,\n  total: 0,\n  currentPage: 1,\n  totalPages: 1,\n  currentMonth: new Date().getMonth() + 1,\n  currentYear: new Date().getFullYear()\n};\nconst getters = {\n  salaryData: state => state.salaryData,\n  loading: state => state.loading,\n  total: state => state.total,\n  currentPage: state => state.currentPage,\n  totalPages: state => state.totalPages,\n  currentMonth: state => state.currentMonth,\n  currentYear: state => state.currentYear\n};\nconst actions = {\n  async fetchSalaryData({\n    commit\n  }, {\n    userId,\n    month,\n    year,\n    page,\n    limit,\n    sortBy,\n    order\n  }) {\n    commit('SET_LOADING', true);\n    try {\n      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/salary`, {\n        params: {\n          userId,\n          month,\n          year,\n          page,\n          limit,\n          sortBy,\n          order\n        },\n        headers: {\n          Authorization: `Bearer ${getAccessToken()}`\n        }\n      });\n      commit('SET_SALARY_DATA', response.data.records);\n      commit('SET_PAGINATION', {\n        total: response.data.totalRecords,\n        currentPage: response.data.currentPage,\n        totalPages: response.data.totalPages\n      });\n      commit('SET_CURRENT_PERIOD', {\n        month: response.data.month,\n        year: response.data.year\n      });\n    } catch (error) {\n      console.error('Error fetching salary data:', error);\n      commit('SET_SALARY_DATA', []);\n    } finally {\n      commit('SET_LOADING', false);\n    }\n  },\n  async exportSalaryToExcel({\n    state\n  }, {\n    userId,\n    month,\n    year\n  }) {\n    try {\n      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/salary`, {\n        params: {\n          userId,\n          month: month || state.currentMonth,\n          year: year || state.currentYear,\n          limit: -1\n        },\n        headers: {\n          Authorization: `Bearer ${getAccessToken()}`\n        }\n      });\n      return response.data.records;\n    } catch (error) {\n      console.error('Error exporting salary data:', error);\n      throw error;\n    }\n  }\n};\nconst mutations = {\n  SET_SALARY_DATA(state, data) {\n    state.salaryData = data;\n  },\n  SET_LOADING(state, loading) {\n    state.loading = loading;\n  },\n  SET_PAGINATION(state, {\n    total,\n    currentPage,\n    totalPages\n  }) {\n    state.total = total;\n    state.currentPage = currentPage;\n    state.totalPages = totalPages;\n  },\n  SET_CURRENT_PERIOD(state, {\n    month,\n    year\n  }) {\n    state.currentMonth = month;\n    state.currentYear = year;\n  }\n};\nexport default {\n  namespaced: true,\n  state,\n  getters,\n  actions,\n  mutations\n};", "map": {"version": 3, "names": ["axios", "getAccessToken", "localStorage", "getItem", "state", "salaryData", "loading", "total", "currentPage", "totalPages", "currentMonth", "Date", "getMonth", "currentYear", "getFullYear", "getters", "actions", "fetchSalaryData", "commit", "userId", "month", "year", "page", "limit", "sortBy", "order", "response", "get", "process", "env", "VUE_APP_API_URL", "params", "headers", "Authorization", "data", "records", "totalRecords", "error", "console", "exportSalaryToExcel", "mutations", "SET_SALARY_DATA", "SET_LOADING", "SET_PAGINATION", "SET_CURRENT_PERIOD", "namespaced"], "sources": ["C:/Users/<USER>/Desktop/swcontrole/SW-Controle-Front/src/store/salary.js"], "sourcesContent": ["import axios from 'axios';\n\nconst getAccessToken = () => localStorage.getItem('accessToken');\n\nconst state = {\n  salaryData: [],\n  loading: false,\n  total: 0,\n  currentPage: 1,\n  totalPages: 1,\n  currentMonth: new Date().getMonth() + 1,\n  currentYear: new Date().getFullYear()\n};\n\nconst getters = {\n  salaryData: (state) => state.salaryData,\n  loading: (state) => state.loading,\n  total: (state) => state.total,\n  currentPage: (state) => state.currentPage,\n  totalPages: (state) => state.totalPages,\n  currentMonth: (state) => state.currentMonth,\n  currentYear: (state) => state.currentYear\n};\n\nconst actions = {\n  async fetchSalaryData({ commit }, { userId, month, year, page, limit, sortBy, order }) {\n    commit('SET_LOADING', true);\n    try {\n      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/salary`, {\n        params: { userId, month, year, page, limit, sortBy, order },\n        headers: {\n          Authorization: `Bearer ${getAccessToken()}`,\n        },\n      });\n     \n      commit('SET_SALARY_DATA', response.data.records);\n      commit('SET_PAGINATION', {\n        total: response.data.totalRecords,\n        currentPage: response.data.currentPage,\n        totalPages: response.data.totalPages\n      });\n      commit('SET_CURRENT_PERIOD', {\n        month: response.data.month,\n        year: response.data.year\n      });\n    } catch (error) {\n      console.error('Error fetching salary data:', error);\n      commit('SET_SALARY_DATA', []);\n    } finally {\n      commit('SET_LOADING', false);\n    }\n  },\n\n  async exportSalaryToExcel({ state }, { userId, month, year }) {\n    try {\n      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/salary`, {\n        params: { \n          userId, \n          month: month || state.currentMonth, \n          year: year || state.currentYear, \n          limit: -1 \n        },\n        headers: {\n          Authorization: `Bearer ${getAccessToken()}`,\n        },\n      });\n      \n      return response.data.records;\n    } catch (error) {\n      console.error('Error exporting salary data:', error);\n      throw error;\n    }\n  }\n};\n\nconst mutations = {\n  SET_SALARY_DATA(state, data) {\n    state.salaryData = data;\n  },\n  SET_LOADING(state, loading) {\n    state.loading = loading;\n  },\n  SET_PAGINATION(state, { total, currentPage, totalPages }) {\n    state.total = total;\n    state.currentPage = currentPage;\n    state.totalPages = totalPages;\n  },\n  SET_CURRENT_PERIOD(state, { month, year }) {\n    state.currentMonth = month;\n    state.currentYear = year;\n  }\n};\n\nexport default {\n  namespaced: true,\n  state,\n  getters,\n  actions,\n  mutations\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,cAAc,GAAGA,CAAA,KAAMC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;AAEhE,MAAMC,KAAK,GAAG;EACZC,UAAU,EAAE,EAAE;EACdC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,CAAC;EACRC,WAAW,EAAE,CAAC;EACdC,UAAU,EAAE,CAAC;EACbC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC;EACvCC,WAAW,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;AACtC,CAAC;AAED,MAAMC,OAAO,GAAG;EACdV,UAAU,EAAGD,KAAK,IAAKA,KAAK,CAACC,UAAU;EACvCC,OAAO,EAAGF,KAAK,IAAKA,KAAK,CAACE,OAAO;EACjCC,KAAK,EAAGH,KAAK,IAAKA,KAAK,CAACG,KAAK;EAC7BC,WAAW,EAAGJ,KAAK,IAAKA,KAAK,CAACI,WAAW;EACzCC,UAAU,EAAGL,KAAK,IAAKA,KAAK,CAACK,UAAU;EACvCC,YAAY,EAAGN,KAAK,IAAKA,KAAK,CAACM,YAAY;EAC3CG,WAAW,EAAGT,KAAK,IAAKA,KAAK,CAACS;AAChC,CAAC;AAED,MAAMG,OAAO,GAAG;EACd,MAAMC,eAAeA,CAAC;IAAEC;EAAO,CAAC,EAAE;IAAEC,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC,IAAI;IAAEC,KAAK;IAAEC,MAAM;IAAEC;EAAM,CAAC,EAAE;IACrFP,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC;IAC3B,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,YAAY,EAAE;QAC3EC,MAAM,EAAE;UAAEZ,MAAM;UAAEC,KAAK;UAAEC,IAAI;UAAEC,IAAI;UAAEC,KAAK;UAAEC,MAAM;UAAEC;QAAM,CAAC;QAC3DO,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUhC,cAAc,CAAC,CAAC;QAC3C;MACF,CAAC,CAAC;MAEFiB,MAAM,CAAC,iBAAiB,EAAEQ,QAAQ,CAACQ,IAAI,CAACC,OAAO,CAAC;MAChDjB,MAAM,CAAC,gBAAgB,EAAE;QACvBX,KAAK,EAAEmB,QAAQ,CAACQ,IAAI,CAACE,YAAY;QACjC5B,WAAW,EAAEkB,QAAQ,CAACQ,IAAI,CAAC1B,WAAW;QACtCC,UAAU,EAAEiB,QAAQ,CAACQ,IAAI,CAACzB;MAC5B,CAAC,CAAC;MACFS,MAAM,CAAC,oBAAoB,EAAE;QAC3BE,KAAK,EAAEM,QAAQ,CAACQ,IAAI,CAACd,KAAK;QAC1BC,IAAI,EAAEK,QAAQ,CAACQ,IAAI,CAACb;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDnB,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;IAC/B,CAAC,SAAS;MACRA,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMqB,mBAAmBA,CAAC;IAAEnC;EAAM,CAAC,EAAE;IAAEe,MAAM;IAAEC,KAAK;IAAEC;EAAK,CAAC,EAAE;IAC5D,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,YAAY,EAAE;QAC3EC,MAAM,EAAE;UACNZ,MAAM;UACNC,KAAK,EAAEA,KAAK,IAAIhB,KAAK,CAACM,YAAY;UAClCW,IAAI,EAAEA,IAAI,IAAIjB,KAAK,CAACS,WAAW;UAC/BU,KAAK,EAAE,CAAC;QACV,CAAC;QACDS,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUhC,cAAc,CAAC,CAAC;QAC3C;MACF,CAAC,CAAC;MAEF,OAAOyB,QAAQ,CAACQ,IAAI,CAACC,OAAO;IAC9B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBC,eAAeA,CAACrC,KAAK,EAAE8B,IAAI,EAAE;IAC3B9B,KAAK,CAACC,UAAU,GAAG6B,IAAI;EACzB,CAAC;EACDQ,WAAWA,CAACtC,KAAK,EAAEE,OAAO,EAAE;IAC1BF,KAAK,CAACE,OAAO,GAAGA,OAAO;EACzB,CAAC;EACDqC,cAAcA,CAACvC,KAAK,EAAE;IAAEG,KAAK;IAAEC,WAAW;IAAEC;EAAW,CAAC,EAAE;IACxDL,KAAK,CAACG,KAAK,GAAGA,KAAK;IACnBH,KAAK,CAACI,WAAW,GAAGA,WAAW;IAC/BJ,KAAK,CAACK,UAAU,GAAGA,UAAU;EAC/B,CAAC;EACDmC,kBAAkBA,CAACxC,KAAK,EAAE;IAAEgB,KAAK;IAAEC;EAAK,CAAC,EAAE;IACzCjB,KAAK,CAACM,YAAY,GAAGU,KAAK;IAC1BhB,KAAK,CAACS,WAAW,GAAGQ,IAAI;EAC1B;AACF,CAAC;AAED,eAAe;EACbwB,UAAU,EAAE,IAAI;EAChBzC,KAAK;EACLW,OAAO;EACPC,OAAO;EACPwB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}