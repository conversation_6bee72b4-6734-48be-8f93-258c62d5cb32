const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const db = require('./models');
const authRoutes = require('./routes/authRoutes');
const agentRoutes = require('./routes/agentRoutes');
const scheduleRoutes = require('./routes/scheduleRoutes');
const projectRoutes = require('./routes/projetRoutes');
const presenceRoutes = require('./routes/presenceRoutes');
const penaliteRoutes = require('./routes/penaliteRoutes');
const autorisationRoutes = require('./routes/autorisationRoutes');
const congeRoutes = require('./routes/congeRoutes');
const calculeRoutes = require('./routes/calculeRoutes');
const cookieParser = require('cookie-parser');
const cronjob = require('node-cron');
const { updateSchedule } = require('./cron/scheduleCron');
const { calculateLeaveBalanceForAllUsers } = require('./cron/leaveBalanceCron');
const { createNewUserInfoEntries } = require('./cron/UserInfoCron');

const app = express();

app.use(cors());
app.use(cookieParser());
app.use(bodyParser.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use('/api/auth', authRoutes);
app.use('/api/agents', agentRoutes);
app.use('/api/schedules', scheduleRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api', presenceRoutes);
app.use('/api', penaliteRoutes);
app.use('/api', autorisationRoutes);
app.use('/api', congeRoutes);
app.use('/api', calculeRoutes);
const corsOptions = {
  origin: process.env.NODE_ENV === 'production' 
  ? ['https://rh.app-kamilex.com/'] 
  : ['http://localhost:3000', 'http://localhost:8080'],
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Requested-With', 'Accept', 'Accept-Version'],
  credentials: true,
  optionsSuccessStatus: 200,
  exposedHeaders: ['Authorization', 'Set-Cookie']
};

app.use(cors(corsOptions));
const PORT = process.env.PORT || 3000;

// Function to initialize cron jobs
function initializeCronJobs() {
  const enableCronJobs = process.env.ENABLE_CRON_JOBS !== 'false';
  const timezone = process.env.TZ || 'Africa/Tunis';

  if (!enableCronJobs) {
    console.log('Cron jobs are disabled via environment variable');
    return;
  }

  console.log(`Initializing cron jobs with timezone: ${timezone}`);

  // Run schedule update immediately on startup
  console.log('Running initial schedule update...');
  updateSchedule().catch(error => {
    console.error('Error in initial schedule update:', error);
  });

  // Schedule daily schedule update at midnight
  cronjob.schedule('0 0 * * *', async () => {
    console.log(`[${new Date().toISOString()}] Running daily schedule update...`);
    try {
      await updateSchedule();
      console.log(`[${new Date().toISOString()}] Daily schedule update completed.`);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error in daily schedule update:`, error);
    }
  }, {
    timezone: timezone,
    scheduled: true
  });

  // Schedule leave balance calculation cron job (daily at 11 PM)
  cronjob.schedule('0 23 * * *', async () => {
    console.log(`[${new Date().toISOString()}] Starting leave balance calculation...`);
    try {
      await calculateLeaveBalanceForAllUsers();
      console.log(`[${new Date().toISOString()}] Leave balance calculation completed.`);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error in leave balance calculation:`, error);
    }
  }, {
    timezone: timezone,
    scheduled: true
  });

  // Schedule create new user info entries cron job (yearly on January 1st)
  cronjob.schedule('0 0 1 1 *', async () => {
    console.log(`[${new Date().toISOString()}] Starting yearly user info creation...`);
    try {
      await createNewUserInfoEntries();
      console.log(`[${new Date().toISOString()}] Yearly user info creation completed.`);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error in yearly user info creation:`, error);
    }
  }, {
    timezone: timezone,
    scheduled: true
  });

  console.log('All cron jobs initialized successfully');
}

db.sequelize.sync().then(() => {
  app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`Timezone: ${process.env.TZ || 'Africa/Tunis'}`);

    // Initialize cron jobs
    initializeCronJobs();
  });
});
/*{ alter: true }*/