import axios from 'axios';

const getAccessToken = () => localStorage.getItem('accessToken');

const state = {
  salaryData: [],
  loading: false,
  total: 0,
  currentPage: 1,
  totalPages: 1,
  currentMonth: new Date().getMonth() + 1,
  currentYear: new Date().getFullYear()
};

const getters = {
  salaryData: (state) => state.salaryData,
  loading: (state) => state.loading,
  total: (state) => state.total,
  currentPage: (state) => state.currentPage,
  totalPages: (state) => state.totalPages,
  currentMonth: (state) => state.currentMonth,
  currentYear: (state) => state.currentYear
};

const actions = {
  async fetchSalaryData({ commit }, { userId, month, year, page, limit, sortBy, order }) {
    commit('SET_LOADING', true);
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/salary`, {
        params: { userId, month, year, page, limit, sortBy, order },
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
        },
      });
     
      commit('SET_SALARY_DATA', response.data.records);
      commit('SET_PAGINATION', {
        total: response.data.totalRecords,
        currentPage: response.data.currentPage,
        totalPages: response.data.totalPages
      });
      commit('SET_CURRENT_PERIOD', {
        month: response.data.month,
        year: response.data.year
      });
    } catch (error) {
      console.error('Error fetching salary data:', error);
      commit('SET_SALARY_DATA', []);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async exportSalaryToExcel({ state }, { userId, month, year }) {
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/salary`, {
        params: { 
          userId, 
          month: month || state.currentMonth, 
          year: year || state.currentYear, 
          limit: -1 
        },
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
        },
      });
      
      return response.data.records;
    } catch (error) {
      console.error('Error exporting salary data:', error);
      throw error;
    }
  }
};

const mutations = {
  SET_SALARY_DATA(state, data) {
    state.salaryData = data;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  SET_PAGINATION(state, { total, currentPage, totalPages }) {
    state.total = total;
    state.currentPage = currentPage;
    state.totalPages = totalPages;
  },
  SET_CURRENT_PERIOD(state, { month, year }) {
    state.currentMonth = month;
    state.currentYear = year;
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
};
