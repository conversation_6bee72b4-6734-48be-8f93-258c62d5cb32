const jwt = require('jsonwebtoken');
const { isTokenBlacklisted } = require('./authBlackList/blacklist');

const verifyToken = (req, res, next) => {
  const authHeader = req.headers && req.headers['authorization'];
  const token = authHeader ? authHeader.split(' ')[1] : null;

  if (!token) {
    return res.status(401).json({ message: 'Access token is missing or invalid' });
  }

  if (isTokenBlacklisted(token)) {
    return res.status(401).json({ error: 'Token blacklisted' });
  }

  jwt.verify(token, 'your_access_token_secret', (err, decoded) => {
    if (err) {
      return res.status(401).json({ message: 'Invalid token' });
    }

    req.user = decoded; // Attach decoded user data to the request object
    next();
  });
};

const authorizeRoles = (...allowedRoles) => (req, res, next) => {
  const { user } = req;
  if (!user || !allowedRoles.includes(user.role)) {
    return res.status(403).json({ message: 'Access forbidden: insufficient permissions' });
  }
  next();
};

module.exports = { verifyToken, authorizeRoles };
