import { login } from '@/api/auth';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import router from '../router';

const state = {
  accessToken: localStorage.getItem('accessToken'),
  error: null,
  loading: false
};

const mutations = {
  SET_ACCESS_TOKEN(state, token) {
    state.accessToken = token;
    if (token) {
      localStorage.setItem('accessToken', token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      localStorage.removeItem('accessToken');
      delete axios.defaults.headers.common['Authorization'];
    }
  },
  SET_ERROR(state, error) {
    state.error = error;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  }
};

const actions = {
  async login_user({ commit }, credentials) {
    commit('SET_LOADING', true);
    try {
      const response = await login(credentials);
      const { accessToken } = response;

      // Validate token contents
      const decoded = jwtDecode(accessToken);
      if (!decoded.id || !decoded.role) {
        throw new Error('Invalid token contents');
      }

      commit('SET_ACCESS_TOKEN', accessToken);
      return decoded;

    } catch (error) {
      console.error('Login Error:', error);
      commit('SET_ERROR', error.message);
      commit('SET_ACCESS_TOKEN', null);
      throw error;
    } finally {
      commit('SET_LOADING', false);
    }
  },

  logout({ commit }) {
    commit('SET_ACCESS_TOKEN', null);
    router.push('/login');
  },

  async forgotPassword({ commit }, email) {
    try {
      const response = await axios.patch(
        `${process.env.VUE_APP_API_URL}api/auth/forgotPassword`, 
        { email }
      );
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || 'Password reset failed';
      commit('SET_ERROR', message);
      throw error;
    }
  },

  clearError({ commit }) {
    commit('SET_ERROR', null);
  },

  async resetPasswordProfile({ commit, dispatch }, { id, password }) {
    commit('SET_LOADING', true);
    try {
      const response = await axios.put(
        `${process.env.VUE_APP_API_URL}api/auth/resetpasswordprofile`,
        { id, password }
      );
      
      if (response.data.success) {
        dispatch('showSnackbar', {
          text: response.data.message,
          color: response.data.type
        });
        return response.data;
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to reset password';
      dispatch('showSnackbar', {
        text: errorMessage,
        color: 'error'
      });
      commit('SET_ERROR', errorMessage);
      throw new Error(errorMessage);
    } finally {
      commit('SET_LOADING', false);
    }
  }
};

const getters = {
  currentUser: (state) => {
    if (!state.accessToken) return null;
    try {
      return jwtDecode(state.accessToken);
    } catch {
      return null;
    }
  },
  
  isLoggedIn: (state) => {
    return !!state.accessToken;
  },

  userId: (state, getters) => {
    return getters.currentUser?.id || null;
  },

  userRole: (state, getters) => {
    return getters.currentUser?.role || null;
  },

  authenticatedUser: (state, getters) => {
    return getters.currentUser;
  }
};

// Initialize axios headers if token exists
if (state.accessToken) {
  axios.defaults.headers.common['Authorization'] = `Bearer ${state.accessToken}`;
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};