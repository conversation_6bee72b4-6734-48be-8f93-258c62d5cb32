const cron = require('node-cron');
require('dotenv').config();

console.log('Testing cron job functionality...');
console.log('Current time:', new Date().toISOString());
console.log('Timezone:', process.env.TZ || 'system default');
console.log('Environment:', process.env.NODE_ENV || 'development');

// Test cron job that runs every minute
const testJob = cron.schedule('* * * * *', () => {
  console.log(`[${new Date().toISOString()}] Test cron job executed successfully!`);
}, {
  scheduled: false,
  timezone: process.env.TZ || 'Africa/Tunis'
});

console.log('Starting test cron job (runs every minute)...');
testJob.start();

// Test the actual cron functions
const { updateSchedule } = require('./cron/scheduleCron');
const { calculateLeaveBalanceForAllUsers } = require('./cron/leaveBalanceCron');
const { createNewUserInfoEntries } = require('./cron/UserInfoCron');

// Test schedule update function
console.log('\nTesting schedule update function...');
updateSchedule()
  .then(() => {
    console.log('✓ Schedule update function works');
  })
  .catch((error) => {
    console.error('✗ Schedule update function failed:', error);
  });

// Test leave balance calculation (commented out to avoid heavy operations)
// console.log('\nTesting leave balance calculation...');
// calculateLeaveBalanceForAllUsers()
//   .then(() => {
//     console.log('✓ Leave balance calculation works');
//   })
//   .catch((error) => {
//     console.error('✗ Leave balance calculation failed:', error);
//   });

// Test user info creation
console.log('\nTesting user info creation...');
createNewUserInfoEntries()
  .then(() => {
    console.log('✓ User info creation works');
  })
  .catch((error) => {
    console.error('✗ User info creation failed:', error);
  });

// Keep the script running for a few minutes to test the cron job
console.log('\nScript will run for 3 minutes to test cron execution...');
setTimeout(() => {
  console.log('Test completed. Stopping...');
  testJob.stop();
  process.exit(0);
}, 180000); // 3 minutes
