const express = require('express');
const router = express.Router();
const congeController = require('../controllers/congeController');
const { verifyToken, authorizeRoles } = require('../middleware/authMiddleware');

// POST create a new conge
router.post('/conges', verifyToken, authorizeR<PERSON>s('employe'), congeController.createConge);

// GET all conges
router.get('/conges', verifyToken, authorizeRoles('admin'), congeController.getAllConges);

// PUT update a conge by ID
router.put('/conges/:id', verifyToken, authorizeRoles('employe'), congeController.updateConge);

// DELETE delete a conge by ID
router.delete('/conges/:id', verifyToken, authorizeRoles('employe'), congeController.deleteConge);

// GET check conge status
router.get('/conges/status', verifyToken, authorizeRoles('employe'), congeController.checkCongeStatus);

// GET conges for a specific user
router.get('/conge', verifyToken, authorizeRoles('employe'), congeController.getUserConges);

// PUT toggle conge status by ID
router.put('/conge/:id', verifyToken, authorizeRoles('admin'), congeController.toggleCongeStatus);

// GET user penalites related to conge
router.get('/penalitesconge', verifyToken, authorizeRoles('employe'), congeController.getUserPenalites);

module.exports = router;
