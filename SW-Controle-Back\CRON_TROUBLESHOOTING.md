# Cron Jobs Troubleshooting Guide

## Common Issues and Solutions

### 1. <PERSON><PERSON> Jobs Not Running in Production

#### Check Environment Variables
Ensure these environment variables are set in your production `.env` file:
```
NODE_ENV=production
TZ=Africa/Tunis
ENABLE_CRON_JOBS=true
```

#### Check Server Logs
Look for these log messages when the server starts:
```
Server is running on port 3000
Environment: production
Timezone: Africa/Tunis
Initializing cron jobs with timezone: Africa/Tunis
Running initial schedule update...
All cron jobs initialized successfully
```

### 2. Timezone Issues

#### Symptoms
- Cron jobs running at wrong times
- Inconsistent execution

#### Solution
1. Set the `TZ` environment variable: `TZ=Africa/Tunis`
2. Restart the application
3. Verify timezone in logs

### 3. Database Connection Issues

#### Symptoms
- Cron jobs start but fail with database errors
- "Connection refused" or "Access denied" errors

#### Solution
1. Verify database credentials in `.env`
2. Check database server is running
3. Test database connection manually

### 4. Memory/Resource Issues

#### Symptoms
- Cron jobs stop working after some time
- Server crashes during cron execution

#### Solution
1. Monitor server resources (RAM, CPU)
2. Check for memory leaks in cron functions
3. Consider running heavy operations less frequently

## Testing Cron Jobs

### Manual Testing
Run the test script to verify cron functionality:
```bash
node test-cron.js
```

### Individual Function Testing
Test each cron function separately:
```javascript
// Test schedule update
const { updateSchedule } = require('./cron/scheduleCron');
updateSchedule().then(() => console.log('Success')).catch(console.error);

// Test leave balance calculation
const { calculateLeaveBalanceForAllUsers } = require('./cron/leaveBalanceCron');
calculateLeaveBalanceForAllUsers().then(() => console.log('Success')).catch(console.error);

// Test user info creation
const { createNewUserInfoEntries } = require('./cron/UserInfoCron');
createNewUserInfoEntries().then(() => console.log('Success')).catch(console.error);
```

## Cron Schedule Reference

| Cron Job | Schedule | Description |
|----------|----------|-------------|
| Schedule Update | `0 0 * * *` | Daily at midnight |
| Leave Balance Calculation | `0 23 * * *` | Daily at 11 PM |
| User Info Creation | `0 0 1 1 *` | Yearly on January 1st |

## Production Deployment Checklist

1. ✅ Set correct environment variables
2. ✅ Verify timezone configuration
3. ✅ Test database connectivity
4. ✅ Run manual cron tests
5. ✅ Monitor logs for cron execution
6. ✅ Set up log rotation for production
7. ✅ Configure monitoring/alerting for cron failures

## Monitoring Cron Jobs

### Log Patterns to Watch
- `[TIMESTAMP] Running daily schedule update...`
- `[TIMESTAMP] Daily schedule update completed.`
- `[TIMESTAMP] Starting leave balance calculation...`
- `[TIMESTAMP] Leave balance calculation completed.`

### Error Patterns
- `Error in daily schedule update:`
- `Error in leave balance calculation:`
- `Error in yearly user info creation:`

## Emergency Procedures

### If Cron Jobs Stop Working
1. Check server logs for errors
2. Verify environment variables
3. Test database connectivity
4. Restart the application
5. Run manual tests with `test-cron.js`

### If Database Operations Fail
1. Check database server status
2. Verify connection credentials
3. Check for database locks or deadlocks
4. Monitor database performance

## Contact Information
For additional support, check the application logs and contact the development team.
