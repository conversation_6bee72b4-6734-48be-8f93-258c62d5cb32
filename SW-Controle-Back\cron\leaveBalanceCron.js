const { Op,sequelize,Sequelize} = require('sequelize');
const { User, Penalite, Absence, Autorisation, Presence,Project,Conge,UserInfo,Schedule,UserProject } = require('../models');
const moment = require('moment');

// Helper function to calculate current sanctions
function calculateCurrentSanctions(user) {
  let totalSanctions = 0;

  // Calculate sanctions from penalties
  if (user.Penalites && user.Penalites.length > 0) {
    totalSanctions += user.Penalites.reduce((sum, penalite) => sum + (penalite.nbrDeJour || 0), 0);
  }

  // Calculate sanctions from absences (1 day per absence)
  if (user.Absences && user.Absences.length > 0) {
    totalSanctions += user.Absences.length;
  }

  return totalSanctions;
}

// Helper function to calculate current leaves taken
function calculateCurrentLeavesTaken(user) {
  let totalLeavesTaken = 0;

  // Calculate leaves from approved congés
  if (user.Conges && user.Conges.length > 0) {
    totalLeavesTaken += user.Conges.reduce((sum, conge) => sum + (conge.nbrDeJour || 0), 0);
  }

  return totalLeavesTaken;
}

exports.calculateLeaveBalanceForAllUsers = async () => {
  try {
    const currentYear = new Date().getFullYear();
    const startOfYear = moment.utc(`${currentYear}-01-01`).startOf('year');
    const endOfYear = moment.utc(`${currentYear}-12-31`).endOf('year');

    const users = await User.findAll({
      where: { role: 'employe' },
      include: [
        {
          model: Penalite,
          attributes: ['nbrDeJour'],
          where: {
            startDate: { [Op.lte]: endOfYear },
            endDate: { [Op.gte]: startOfYear }
          },
          required: false
        },
        {
          model: Absence,
          attributes: ['id'],
          where: {
            date: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Autorisation,
          attributes: ['nbrheures'],
          where: {
            createdAt: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Presence,
          attributes: ['retardtotal'],
          where: {
            date: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Conge,
          where: { 
            status: 'accepté',
            startDate: { [Op.lte]: endOfYear },
            endDate: { [Op.gte]: startOfYear }
          },
          attributes: ['nbrDeJour'],
          required: false
        },
      ],
      attributes: ['name', 'id'],
      distinct: true
    });

    const leaveBalancesPromises = users.map(async (user) => {
      const currentSanctions = calculateCurrentSanctions(user);
      const currentLeavesTaken = calculateCurrentLeavesTaken(user);

      let userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: currentYear } });
      if (!userInfo) {
        userInfo = await UserInfo.create({
          UserId: user.id,
          year: currentYear,
          months: 0,
          soldeAncienConge: 0,
          congePrise: 0,
          sanctions: 0,
          resteConge: 0,
          soldeConge: 0,
        });
      }

      const monthsDone = userInfo.months || 0;
      const soldeAncienConge = userInfo.soldeAncienConge || 0;

      const earnedLeave = monthsDone * 1.75;
      const totalSanctions = currentSanctions;
      const leavesTaken = currentLeavesTaken;
      const totalUsed = leavesTaken + totalSanctions;

      let RESTANCIENCONGE = 0;
      let RESTCONGE = 0;

      if (totalUsed <= soldeAncienConge) {
        RESTANCIENCONGE = soldeAncienConge - totalUsed;
        RESTCONGE = earnedLeave;
      } else {
        RESTANCIENCONGE = 0;
        RESTCONGE = earnedLeave - (totalUsed - soldeAncienConge);
      }

      RESTANCIENCONGE = Math.round(RESTANCIENCONGE * 100) / 100;
      RESTCONGE = Math.round(RESTCONGE * 100) / 100;

      if (
        userInfo.congePrise === null ||
        userInfo.sanctions === null ||
        userInfo.resteConge === null
      ) {
        await UserInfo.update({
          congePrise: leavesTaken,
          sanctions: totalSanctions,
          resteConge: RESTCONGE,
          soldeAncienConge: RESTANCIENCONGE
        }, {
          where: {
            UserId: user.id,
            year: currentYear 
          }
        });
        userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: currentYear } });
      } else {
        const sanctionsDifference = currentSanctions - userInfo.sanctions;
        const congePriseDifference = currentLeavesTaken - userInfo.congePrise;

        const updates = {};

        if (currentSanctions > userInfo.sanctions ||currentSanctions < userInfo.sanctions) {
          updates.sanctions = currentSanctions;
        }
        if (currentLeavesTaken > userInfo.congePrise ||currentSanctions < userInfo.sanctions) {
          updates.congePrise = currentLeavesTaken;
        }

        if (Object.keys(updates).length > 0) {
          let newRESTANCIENCONGE = userInfo.soldeAncienConge;
          let newRESTCONGE = userInfo.resteConge;

          if (sanctionsDifference > 0) {
            if (newRESTANCIENCONGE >= sanctionsDifference) {
              newRESTANCIENCONGE -= sanctionsDifference;
            } else {
              const remainingDifference = sanctionsDifference - newRESTANCIENCONGE;
              newRESTANCIENCONGE = 0;
              newRESTCONGE -= remainingDifference;
            }
          }
          if (congePriseDifference > 0) {
            if (newRESTANCIENCONGE >= congePriseDifference) {
              newRESTANCIENCONGE -= congePriseDifference;
            } else {
              const remainingDifference = congePriseDifference - newRESTANCIENCONGE;
              newRESTANCIENCONGE = 0;
              newRESTCONGE -= remainingDifference;
            }
          }

          updates.soldeAncienConge = newRESTANCIENCONGE;
          updates.resteConge = newRESTCONGE;

          await UserInfo.update(updates, { where: { UserId: user.id, year: currentYear } });
          userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: currentYear } });
        }
      }

      return {
        id: user.id,
        name: user.name,
        monthsDone: userInfo.months,
        SOLDECONGE: userInfo.soldeConge || earnedLeave,
        CONGEPRISE: userInfo.congePrise,
        sanction: userInfo.sanctions,
        RESTANCIENCONGE: userInfo.soldeAncienConge,
        RESTCONGE: userInfo.resteConge
      };
    });

    await Promise.all(leaveBalancesPromises);
    console.log('Leave balances calculated for all users.');
  } catch (error) {
    console.error('Error calculating leave balances for all users:', error);
  }
};