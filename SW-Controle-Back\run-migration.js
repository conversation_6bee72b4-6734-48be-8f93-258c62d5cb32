const { Sequelize } = require('sequelize');
const config = require('./config/database');

// Create Sequelize instance
const sequelize = new Sequelize(config.database, config.username, config.password, {
  host: config.host,
  dialect: config.dialect,
  logging: console.log
});

async function runMigration() {
  try {
    console.log('Starting migration to add tallying modification flags...');
    
    // Add the new boolean columns
    await sequelize.getQueryInterface().addColumn('presences', 'entreeModified', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false
    });
    console.log('Added entreeModified column');

    await sequelize.getQueryInterface().addColumn('presences', 'sortieModified', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false
    });
    console.log('Added sortieModified column');

    await sequelize.getQueryInterface().addColumn('presences', 'entree1Modified', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false
    });
    console.log('Added entree1Modified column');

    await sequelize.getQueryInterface().addColumn('presences', 'sortie1Modified', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false
    });
    console.log('Added sortie1Modified column');

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await sequelize.close();
  }
}

runMigration();
