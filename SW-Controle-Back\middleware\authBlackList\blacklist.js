const fs = require('fs');
const path = require('path');

// File to store the blacklist
const blacklistFile = path.join(__dirname, 'blacklist.json');

// In-memory blacklist
let blacklist = new Map();

// Load blacklist from file at startup
function loadBlacklist() {
    if (fs.existsSync(blacklistFile)) {
      const data = fs.readFileSync(blacklistFile, 'utf8');
      const parsedData = JSON.parse(data); // Parse JSON to an array
      if (Array.isArray(parsedData)) {
        blacklist = new Map(parsedData); // Convert array to Map
        console.log('Blacklist loaded from file');
      } else {
        console.error('Invalid data format in blacklist file. Starting with an empty blacklist.');
      }
    } else {
      console.log('No existing blacklist file found, starting fresh.');
    }
  }

// Save blacklist to file
function saveBlacklist() {
  fs.writeFileSync(blacklistFile, JSON.stringify([...blacklist])); // Convert Map to JSON
  console.log('Blacklist saved to file');
}

// Add a token to the blacklist
function addToBlacklist(token) {
  blacklist.set(token, Date.now());
  saveBlacklist(); // Persist changes
}

// Check if a token is blacklisted
function isTokenBlacklisted(token) {
    console.error(blacklist.has(token))
  return blacklist.has(token);
  
}

// Initialize blacklist on module load
loadBlacklist();

module.exports = { addToBlacklist, isTokenBlacklisted };