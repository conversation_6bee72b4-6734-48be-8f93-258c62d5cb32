<template>
  <v-container>
    <v-card class="mx-auto" max-width="500">
      <v-card-title>
        <span class="headline">{{ agentInfo.name }}</span>
      </v-card-title>
      <v-card-subtitle>{{ agentInfo.email }}</v-card-subtitle>
      <v-divider></v-divider>
      <v-card-text>
        <v-form ref="form" v-model="valid">
          <v-list-item>
            <v-list-item-title>Nombre de mois éffectués:</v-list-item-title>
            <v-list-item-subtitle>{{ agentInfo.months }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <v-list-item-title>Solde Ancien Conge:</v-list-item-title>
            <v-list-item-subtitle>{{ agentInfo.soldeAncienConge }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <v-list-item-title>Addresse:</v-list-item-title>
            <v-list-item-subtitle>{{ agentInfo.address }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <v-list-item-title>téléphone:</v-list-item-title>
            <v-list-item-subtitle>{{ agentInfo.phoneNumber }}</v-list-item-subtitle>
          </v-list-item>

          <v-text-field 
            v-if="showPasswordField" 
            v-model="newPassword" 
            :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
            :type="showPassword ? 'text' : 'password'" 
            label="Mot de passe" 
            :rules="showPasswordField ? passwordRules : []"
            @click:append="togglePassword"   
            density="compact"
            variant="outlined"
          />

          <v-btn text color="primary" class="mt-3" @click="togglePasswordReset">
            {{ showPasswordField ? 'annuler le resete du mot de passe' : 'reseter le mot de passe' }}
          </v-btn>
        </v-form>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn v-if="showPasswordField" color="primary" @click="saveNewPassword" :disabled="!valid">
          Mettre à jour le mot de passe
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- Snackbar for Notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          text
          @click="snackbar.show = false"
        >
          Fermer
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  data() {
    return {
      valid: true,
      showPasswordField: false,
      newPassword: '',
      password: '',
      showPassword: false,
      passwordRules: [
        v => !!v || 'Le mot de passe est requis',
        v => (v.length >= 8) || 'Le mot de passe doit contenir au moins 8 caractères'
      ],
      snackbar: {
        show: false,
        message: '',
        color: 'success'
      }
    };
  },

  computed: {
    ...mapGetters('agent', ['agentInfo'])
  },
  methods: {
    ...mapActions('auth', ['resetPasswordProfile']),
    togglePasswordReset() {
      this.showPasswordField = !this.showPasswordField;
      if (!this.showPasswordField) {
        this.newPassword = '';
        this.$refs.form.resetValidation();
      }
    },
    togglePassword() {
      this.showPassword = !this.showPassword;
    },
    async saveNewPassword() {
      try {
        if (!this.valid) {
          this.snackbar = {
            show: true,
            message: 'Veuillez remplir correctement le formulaire',
            color: 'error'
          };
          return;
        }
        const userId = this.$store.getters['auth/currentUser'].id;
        if (!userId) {
          this.snackbar = {
            show: true,
            message: 'ID utilisateur non trouvé',
            color: 'error'
          };
          return;
        }
        await this.$store.dispatch('auth/resetPasswordProfile', { 
          id: userId, 
          password: this.newPassword 
        });
        this.showPasswordField = false;
        this.newPassword = '';
        this.$refs.form.resetValidation();
        this.snackbar = {
          show: true,
          message: 'Mot de passe mis à jour avec succès',
          color: 'success'
        };
      } catch (error) {
        console.error('Error updating password:', error);
        this.snackbar = {
          show: true,
          message: error.response?.data?.message || 'Erreur lors de la mise à jour du mot de passe',
          color: 'error'
        };
      }
    }
  },
  async mounted() {

  },
  async created() {
    await this.$store.dispatch('agent/fetchAgentInfo', this.$store.getters['auth/currentUser'].id);
  }
};
</script>

<style scoped>
.v-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>