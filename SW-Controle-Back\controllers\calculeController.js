const { Op,sequelize,Sequelize} = require('sequelize');
const { User, <PERSON><PERSON>te, Absence, Autorisation, Presence,Project,Conge,UserInfo,Schedule,UserProject } = require('../models');
const moment = require('moment');

exports.getRetardData = async (req, res) => {
  try {
    const { userId, month, year: providedYear } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const sortBy = req.query.sortBy || "createdAt";
    const order = req.query.order || "ASC";

    // Construct where clause
    const whereClause = {};

    // Add userId filter if provided
    if (userId) {
      whereClause.UserId = userId;
    }

    // Determine the year to use
    const selectedYear = providedYear ? parseInt(providedYear) : moment().year();

    // Determine date range based on year and month parameters
    let startDate, endDate;

    if (month) {
      // If month is provided, filter by that month in the selected year
      startDate = moment(`${selectedYear}-${month}`, "YYYY-MM").startOf("month");
      endDate = moment(startDate).endOf("month");
    } else {
      // If no month is provided, filter by the entire selected year
      startDate = moment(`${selectedYear}-01-01`, "YYYY-MM-DD").startOf("year");
      endDate = moment(`${selectedYear}-12-31`, "YYYY-MM-DD").endOf("year");
    }

    // Add createdAt filter to whereClause
    whereClause.createdAt = {
      [Op.between]: [startDate.toDate(), endDate.toDate()],
    };

    // Determine the order array based on sortBy
    let orderArray;
    if (sortBy === "User.name") {
      orderArray = [
        [{ model: User, as: "User" }, "name", order],
        ["createdAt", "ASC"],
      ];
    } else {
      orderArray = [[sortBy, order]];
    }

    // Set limit and offset only if limit is not -1
    const queryOptions = {
      where: whereClause,
      attributes: ["id", "retardm", "retardam", "retardtotal", "createdAt"],
      include: [
        {
          model: User,
          as: "User",
          attributes: ["id", "name", "email"],
        },
      ],
      order: orderArray,
    };

    if (limit !== -1) {
      queryOptions.limit = limit;
      queryOptions.offset = (page - 1) * limit;
    }

    const { rows, count } = await Presence.findAndCountAll(queryOptions);

    // Format the retard data
    const formattedRows = rows.map((row) => {
      const formatRetard = (minutes) => {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return `${hours}h ${remainingMinutes}m`;
      };

      return {
        createdAt: moment(row.createdAt).format("YYYY-MM-DD"),
        User: {
          name: row.User.name,
        },
        retardm: formatRetard(row.retardm),
        retardam: formatRetard(row.retardam),
        retardtotal: formatRetard(row.retardtotal),
      };
    });

    // Prepare response
    res.status(200).json({
      totalRecords: count,
      totalPages: limit === -1 ? 1 : Math.ceil(count / limit),
      currentPage: page,
      records: formattedRows,
    });
  } catch (error) {
    console.error("Error fetching retard data: ", error);
    res.status(500).json({ error: "An error occurred while fetching retard data" });
  }
};

exports.calculateLeaveBalance = async (req, res) => {
  try {
    const { userId, page = 1, limit = 10, sortBy, order = 'asc', year = new Date().getFullYear() } = req.query;
    const parsedUserId = userId ? parseInt(userId) : undefined;
    const parsedPage = parseInt(page);
    const parsedLimit = parseInt(limit);
    const parsedYear = parseInt(year);

    const startOfYear = moment.utc(`${parsedYear}-01-01`).startOf('year');
    const endOfYear = moment.utc(`${parsedYear}-12-31`).endOf('year');

    const userWhereClause = { role: 'employe' };
    if (parsedUserId) {
      userWhereClause.id = parsedUserId;
    }

    let paginationOptions = {};
    if (parsedLimit !== -1) {
      const offset = (parsedPage - 1) * parsedLimit;
      paginationOptions = { offset, limit: parsedLimit };
    }

    const users = await User.findAll({
      where: userWhereClause,
      include: [
        {
          model: Penalite,
          attributes: ['nbrDeJour'],
          where: {
            startDate: { [Op.lte]: endOfYear },
            endDate: { [Op.gte]: startOfYear }
          },
          required: false
        },
        {
          model: Absence,
          attributes: ['id'],
          where: {
            date: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Autorisation,
          attributes: ['nbrheures'],
          where: {
            createdAt: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Presence,
          attributes: ['retardtotal'],
          where: {
            date: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Conge,
          where: { 
            status: 'accepté',
            startDate: { [Op.lte]: endOfYear },
            endDate: { [Op.gte]: startOfYear }
          },
          attributes: ['nbrDeJour'],
          required: false
        },
      ],
      attributes: ['name', 'id'],
      distinct: true,
      ...paginationOptions
    });

    const count = await User.count({ where: userWhereClause });
    const leaveBalancesPromises = users.map(async (user) => {
      const currentSanctions = calculateCurrentSanctions(user);
      const currentLeavesTaken = calculateCurrentLeavesTaken(user);

      let userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: parsedYear  } });
      if (!userInfo) {
        await UserInfo.create({
          UserId: user.id,
          year: parsedYear ,
          months: 0,
          soldeAncienConge: 0,
          congePrise: 0,
          sanctions: 0,
          resteConge: 0,
          soldeConge: 0,
        });
      }
      userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: parsedYear  } });
      const monthsDone = userInfo ? userInfo.months || 0 : 0;
      const soldeAncienConge = userInfo ? userInfo.soldeAncienConge || 0 : 0;

      const earnedLeave = monthsDone * 1.75;
      const totalSanctions = currentSanctions;
      const leavesTaken = currentLeavesTaken;
      const totalUsed = leavesTaken + totalSanctions;

      let RESTANCIENCONGE = 0;
      let RESTCONGE = 0;

      if (totalUsed <= soldeAncienConge) {
        RESTANCIENCONGE = soldeAncienConge - totalUsed;
        RESTCONGE = earnedLeave;
      } else {
        RESTANCIENCONGE = 0;
        RESTCONGE = earnedLeave - (totalUsed - soldeAncienConge);
      }

      RESTANCIENCONGE = Math.round(RESTANCIENCONGE * 100) / 100;
      RESTCONGE = Math.round(RESTCONGE * 100) / 100;

   
     if (
  userInfo.congePrise === null ||
  userInfo.sanctions === null ||
  userInfo.resteConge === null
) {
  await UserInfo.update({
    congePrise: leavesTaken,
    sanctions: totalSanctions,
    resteConge: RESTCONGE,
    soldeAncienConge: RESTANCIENCONGE
  }, {
    where: {
      UserId: user.id,
      year: parsedYear 
    }
  });
       userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: parsedYear  } });
      } else {
        const sanctionsDifference = currentSanctions - userInfo.sanctions;
        const congePriseDifference = currentLeavesTaken - userInfo.congePrise;

        const updates = {};

        if (currentSanctions > userInfo.sanctions||currentSanctions < userInfo.sanctions) {
          updates.sanctions = currentSanctions;
        }
        if (currentLeavesTaken > userInfo.congePrise||currentLeavesTaken < userInfo.congePrise) {
          updates.congePrise = currentLeavesTaken;
        }
        if (Object.keys(updates).length > 0) {
          let newRESTANCIENCONGE = userInfo.soldeAncienConge;
          let newRESTCONGE = userInfo.resteConge;
        
          // Handle sanctionsDifference
          if (sanctionsDifference > 0) {
            if (newRESTANCIENCONGE >= sanctionsDifference) {
              newRESTANCIENCONGE -= sanctionsDifference;
            } else {
              const remainingDifference = sanctionsDifference - newRESTANCIENCONGE;
              newRESTANCIENCONGE = 0;
              newRESTCONGE -= remainingDifference;
            }
          } else if (sanctionsDifference < 0) {
            newRESTANCIENCONGE += Math.abs(sanctionsDifference);
          }
        
          // Handle congePriseDifference
          if (congePriseDifference > 0) {
            if (newRESTANCIENCONGE >= congePriseDifference) {
              newRESTANCIENCONGE -= congePriseDifference;
            } else {
              const remainingDifference = congePriseDifference - newRESTANCIENCONGE;
              newRESTANCIENCONGE = 0;
              newRESTCONGE -= remainingDifference;
            }
          } else if (congePriseDifference < 0) {
            newRESTANCIENCONGE += Math.abs(congePriseDifference);
          }
        
          updates.soldeAncienConge = newRESTANCIENCONGE;
          updates.resteConge = newRESTCONGE;
        
          await UserInfo.update(updates, { where: { UserId: user.id, year: parsedYear } });
          userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: parsedYear } });
        }
      }

      return {
        id: user.id,
        name: user.name,
        monthsDone: userInfo.months,
        SOLDECONGE: userInfo.soldeConge || earnedLeave,
        CONGEPRISE: userInfo.congePrise,
        sanction: userInfo.sanctions,
        RESTANCIENCONGE: userInfo.soldeAncienConge,
        RESTCONGE: userInfo.resteConge
      };
    });

    const leaveBalances = await Promise.all(leaveBalancesPromises);

    if (sortBy && leaveBalances.length > 0 && leaveBalances[0].hasOwnProperty(sortBy)) {
      leaveBalances.sort((a, b) => {
        if (a[sortBy] < b[sortBy]) return order === 'asc' ? -1 : 1;
        if (a[sortBy] > b[sortBy]) return order === 'asc' ? 1 : -1;
        return 0;
      });
    }

    const totalPages = parsedLimit === -1 ? 1 : Math.ceil(count / parsedLimit);
    const currentPage = parsedPage;

    res.json({
     leaveBalances,
      pagination: {
        totalItems: count,
        itemsPerPage: parsedLimit === -1 ? count : parsedLimit,
        currentPage,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error calculating leave balances:', error);
    res.status(500).json({ message: 'Failed to calculate leave balances', error: error.message });
  }
};

function calculateCurrentSanctions(user) {
  const penaltiesDays = (user.Penalites || []).reduce((sum, penalty) => sum + (penalty.nbrDeJour || 0), 0);
  const absencesDays = (user.Absences || []).length;
  const totalTardiness = (user.Presences || []).reduce((sum, presence) => sum + (presence.retardtotal || 0), 0);
  const tardinessDays = Math.floor(totalTardiness / 480) || 0;
  const totalAuthorizationMinutes = (user.Autorisations || []).reduce((sum, auth) => {
    const minutes = parseFloat(auth.nbrheures);
    return sum + (isNaN(minutes) ? 0 : minutes);
  }, 0);
  const authorizedAbsenceDays = Math.floor(totalAuthorizationMinutes / 480);
  return penaltiesDays + absencesDays + tardinessDays + authorizedAbsenceDays;
}

function calculateCurrentLeavesTaken(user) {
  return (user.Conges || []).reduce((sum, conge) => sum + (conge.nbrDeJour || 0), 0);
}
exports.calculateLeaveBalanceForUser = async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ message: 'UserID is required' });
    }

    const currentYear = new Date().getFullYear();
    const startOfYear = moment.utc(`${currentYear}-01-01`).startOf('year');
    const endOfYear = moment.utc(`${currentYear}-12-31`).endOf('year');

    const user = await User.findOne({
      where: { id: userId, role: 'employe' },
      include: [
        {
          model: Penalite,
          attributes: ['nbrDeJour'],
          where: {
            startDate: { [Op.lte]: endOfYear },
            endDate: { [Op.gte]: startOfYear }
          },
          required: false
        },
        {
          model: Absence,
          attributes: ['id'],
          where: {
            date: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Autorisation,
          attributes: ['nbrheures'],
          where: {
            createdAt: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Presence,
          attributes: ['retardtotal'],
          where: {
            date: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          required: false
        },
        {
          model: Conge,
          where: { 
            status: 'accepté',
            startDate: { [Op.lte]: endOfYear },
            endDate: { [Op.gte]: startOfYear }
          },
          attributes: ['nbrDeJour'],
          required: false
        },
        {
          model: Project,
          through: { model: UserProject, attributes: [] },
          attributes: ['name']
        }
      ],
      attributes: ['name', 'id']
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const currentSanctions = calculateCurrentSanctions(user);
    const currentLeavesTaken = calculateCurrentLeavesTaken(user);

    let userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: currentYear } });

    if (!userInfo) {
      userInfo = await UserInfo.create({
        UserId: user.id,
        year: currentYear,
        months: 0,
        soldeAncienConge: 0,
        congePrise: 0,
        sanctions: 0,
        resteConge: 0,
        soldeConge: 0,
      });
    }

    const monthsDone = userInfo.months || 0;
    const soldeAncienConge = userInfo.soldeAncienConge || 0;

    const earnedLeave = monthsDone * 1.75;
    const totalSanctions = currentSanctions;
    const leavesTaken = currentLeavesTaken;
    const totalUsed = leavesTaken + totalSanctions;

    const sanctionsDifference = currentSanctions - (userInfo.sanctions || 0);
    const congePriseDifference = currentLeavesTaken - (userInfo.congePrise || 0);

    let newRESTANCIENCONGE = userInfo.soldeAncienConge || 0;
    let newRESTCONGE = userInfo.resteConge || 0;

    if (sanctionsDifference > 0) {
      if (newRESTANCIENCONGE >= sanctionsDifference) {
        newRESTANCIENCONGE -= sanctionsDifference;
      } else {
        const remainingDifference = sanctionsDifference - newRESTANCIENCONGE;
        newRESTANCIENCONGE = 0;
        newRESTCONGE -= remainingDifference;
      }
    }

    if (congePriseDifference > 0) {
      if (newRESTANCIENCONGE >= congePriseDifference) {
        newRESTANCIENCONGE -= congePriseDifference;
      } else {
        const remainingDifference = congePriseDifference - newRESTANCIENCONGE;
        newRESTANCIENCONGE = 0;
        newRESTCONGE -= remainingDifference;
      }
    }

    newRESTANCIENCONGE = Math.round(newRESTANCIENCONGE * 100) / 100;
    newRESTCONGE = Math.round(newRESTCONGE * 100) / 100;

    await UserInfo.update(
      {
        congePrise: currentLeavesTaken,
        sanctions: currentSanctions,
        resteConge: newRESTCONGE,
        soldeAncienConge: newRESTANCIENCONGE,
        soldeConge: earnedLeave
      },
      { where: { UserId: user.id, year: currentYear } }
    );

    userInfo = await UserInfo.findOne({ where: { UserId: user.id, year: currentYear } });

    const leaveBalanceData = {
      id: user.id,
      name: user.name,
      monthsDone: userInfo.months,
      SOLDECONGE: Math.round(earnedLeave),
      CONGEPRISE: userInfo.congePrise,
      sanction: userInfo.sanctions,
      RESTANCIENCONGE: userInfo.soldeAncienConge,
      RESTCONGE: userInfo.resteConge,
      conges: user.Conges || [],
      projects: user.Projects?.map(project => ({ name: project.name })) || []
    };

    res.json(leaveBalanceData);
  } catch (error) {
    console.error('Error calculating leave balance for user:', error);
    res.status(500).json({ message: 'Failed to calculate leave balance', error: error.message });
  }
};

exports.getAbsences = async (req, res) => {
  try {
    const { userId, month, year: providedYear, page = 1, limit: rawLimit = 10, sortBy = 'startDate', order = 'asc' } = req.query;
    const limit = parseInt(rawLimit);

    // Construct where clause
    const whereClause = {};

    // Add userId filter if provided
    if (userId) {
      whereClause.UserId = userId;
    }

    // Determine the year to use
    const selectedYear = providedYear ? parseInt(providedYear) : moment().year();

    // Determine date range based on year and month parameters
    let startDate, endDate;

    if (month) {
      // If month is provided, filter by that month in the selected year
      startDate = moment(`${selectedYear}-${month}-01`, 'YYYY-MM-DD').startOf('month');
      endDate = moment(startDate).endOf('month');
    } else {
      // If no month is provided, filter by the entire selected year
      startDate = moment(`${selectedYear}-01-01`, 'YYYY-MM-DD').startOf('year');
      endDate = moment(`${selectedYear}-12-31`, 'YYYY-MM-DD').endOf('year');
    }

    // Add date filter to whereClause
    whereClause.date = {
      [Op.between]: [startDate.toDate(), endDate.toDate()]
    };

    // Define the query options
    const queryOptions = {
      where: whereClause,
      include: [{ 
        model: User, 
        as: 'User',
        attributes: ['id', 'name'] 
      }],
      order: [[sortBy, order.toUpperCase()]], // Use the provided sortBy and order
    };

    // Fetch all absences without pagination
    const allAbsences = await Absence.findAll(queryOptions);

    // Group absences
    let groupedAbsences = groupAbsences(allAbsences);

    // Apply pagination to grouped absences if limit is not -1
    const totalItems = groupedAbsences.length;
    let paginatedAbsences, totalPages, currentPage;

    if (limit === -1) {
      // Return all elements without pagination
      paginatedAbsences = groupedAbsences;
      totalPages = 1;
      currentPage = 1;
    } else {
      // Apply pagination
      totalPages = Math.ceil(totalItems / limit);
      currentPage = page;
      const offset = (currentPage - 1) * limit;
      paginatedAbsences = groupedAbsences.slice(offset, offset + limit);
    }

    // Send response
    return res.status(200).json({
      absences: paginatedAbsences,
      pagination: {
        totalItems,
        totalPages,
        currentPage,
        limit: limit === -1 ? totalItems : limit,
      }
    });

  } catch (error) {
    console.error('Error fetching absences:', error);
    return res.status(500).json({ message: error.message });
  }
};
function groupAbsences(absences) {
  const groupedAbsences = [];
  let currentGroup = null;

  for (const absence of absences) {
    const currentDate = moment(absence.date);

    if (!currentGroup || 
        currentGroup.UserId !== absence.UserId || 
        currentDate.diff(moment(currentGroup.endDate), 'days') > 1) {
      if (currentGroup) {
        // Calculate the number of days for the group
        currentGroup.days = moment(currentGroup.endDate).diff(moment(currentGroup.startDate), 'days') + 1;
        groupedAbsences.push(currentGroup);
      }
      currentGroup = {
        UserId: absence.UserId,
        name: absence.User.name,
        startDate: absence.date,
        endDate: absence.date,
        raison: absence.raison,
        days: 1 // Default to 1 day when creating a new group
      };
    } else {
      currentGroup.endDate = absence.date;
    }
  }

  if (currentGroup) {
    // Ensure the last group is added
    currentGroup.days = moment(currentGroup.endDate).diff(moment(currentGroup.startDate), 'days') + 1;
    groupedAbsences.push(currentGroup);
  }

  return groupedAbsences;
}

function sortGroupedAbsences(absences, sortBy, order) {
  return absences.sort((a, b) => {
    let comparison = 0;
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'startDate':
      case 'endDate':
        comparison = moment(a[sortBy]).diff(moment(b[sortBy]));
        break;
      case 'raison':
        comparison = a.raison.localeCompare(b.raison);
        break;
      case 'days':
        comparison = a.days - b.days;
        break;
      default:
        comparison = 0;
    }
    return order.toLowerCase() === 'asc' ? comparison : -comparison;
  });
}
function getScheduleType(schedule) {
 if(schedule.isRecuring){
  return ture
 }else{
  return false
 }
}

function calculateTimeDifference(scheduledTime, actualTime) {
  if (!scheduledTime || !actualTime) return 0;
  const scheduled = moment(scheduledTime, 'HH:mm:ss');
  const actual = moment(actualTime, 'HH:mm:ss');
  if (actual.isAfter(scheduled)) {
    return actual.diff(scheduled, 'minutes');
  }
  return 0;
}

function formatMinutes(minutes) {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return `${hours}h ${remainingMinutes}m`;
}

exports.addAbsence = async (req, res) => {
  try {
    const { userId, date, raison } = req.body;

    const schedule = await Schedule.findOne({
      where: { isSelected: true },
     attributes: ['id'],
    });
    if (!userId || !date || !raison) {
      return res.status(400).json({ message: 'All fields (userId, date, raison) are required.' });
    }

    // Validate date format
    const isoDate = new Date(date);
    if (isNaN(isoDate)) {
      return res.status(400).json({ message: 'Invalid date format. Use ISO date format.' });
    }

    // Check if user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found.' });
    }

    // Check if absence already exists
    const existingAbsence = await Absence.findOne({
      where: { UserId: userId, date: date }
    });

    if (existingAbsence) {
      return res.status(409).json({ message: 'Absence already exists for this user on this date.' });
    }

    // Create the new absence
    await Absence.create({
      UserId: userId,
      date: date,
      raison: raison,
      ScheduleId:schedule.id 
    });

    // Optional: Remove presence record (simplified approach)
    await Presence.destroy({
      where: { UserId: userId, date: date }
    });

    return res.status(201).json({ message: 'Absence added successfully.' });
  } catch (error) {
    console.error('Error adding absence:', error);
    return res.status(500).json({ message: error });
  }
};
exports.updateSoldeAncienConge = async (req, res) => {
  try {
    const { id, soldeAncienConge } = req.body;
    const currentYear = new Date().getFullYear();
    const userInfo = await UserInfo.findOne({
      where: { UserId: id, year: currentYear }
    });

    if (!userInfo) {
      return res.status(404).json({ message: 'UserInfo not found for the current year.' });
    }

    userInfo.soldeAncienConge = soldeAncienConge;
    await userInfo.save();

    res.json({ message: 'soldeAncienConge updated successfully.' });
  } catch (error) {
    console.error('Error updating soldeAncienConge:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Calculate salary deductions based on retard rules
function calculateSalaryDeduction(totalRetardMinutes, baseSalary) {
  const workingHoursPerDay = 8;
  const hourlyRate = baseSalary / (workingHoursPerDay * 22); // Assuming 22 working days per month

  let deductedHours = 0;

  if (totalRetardMinutes >= 10 && totalRetardMinutes < 30) {
    deductedHours = 1;
  } else if (totalRetardMinutes >= 30 && totalRetardMinutes < 60) {
    deductedHours = 2;
  } else if (totalRetardMinutes >= 60 && totalRetardMinutes < 120) {
    deductedHours = 3;
  } else if (totalRetardMinutes >= 120) {
    deductedHours = 4;
  }

  const deductedAmount = deductedHours * hourlyRate;
  const remainingSalary = baseSalary - deductedAmount;

  return {
    deductedHours,
    deductedAmount: Math.round(deductedAmount * 100) / 100,
    remainingSalary: Math.round(remainingSalary * 100) / 100,
    hourlyRate: Math.round(hourlyRate * 100) / 100
  };
}

exports.getSalaryCalculations = async (req, res) => {
  try {
    const { userId, month, year: providedYear } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const sortBy = req.query.sortBy || "name";
    const order = req.query.order || "ASC";

    // Determine the year and month to use
    const selectedYear = providedYear ? parseInt(providedYear) : moment().year();
    const selectedMonth = month ? parseInt(month) : moment().month() + 1;

    // Construct where clause for users
    const userWhereClause = { role: 'employe' };
    if (userId) {
      userWhereClause.id = userId;
    }

    // Date range for the selected month
    const startDate = moment(`${selectedYear}-${selectedMonth}`, "YYYY-MM").startOf("month");
    const endDate = moment(startDate).endOf("month");

    // Set limit and offset only if limit is not -1
    const queryOptions = {
      where: userWhereClause,
      attributes: ['id', 'name', 'email', 'salaire'],
      include: [
        {
          model: Presence,
          attributes: ['retardtotal', 'date'],
          where: {
            date: {
              [Op.between]: [startDate.toDate(), endDate.toDate()]
            }
          },
          required: false
        }
      ]
    };

    if (limit !== -1) {
      queryOptions.limit = limit;
      queryOptions.offset = (page - 1) * limit;
    }

    // Determine the order array based on sortBy
    let orderArray;
    if (sortBy === "name") {
      orderArray = [["name", order]];
    } else if (sortBy === "salaire") {
      orderArray = [["salaire", order]];
    } else {
      orderArray = [["name", "ASC"]];
    }
    queryOptions.order = orderArray;

    const { rows: users, count } = await User.findAndCountAll(queryOptions);

    // Calculate salary data for each user
    const salaryCalculations = users.map(user => {
      const totalRetardMinutes = (user.Presences || []).reduce((sum, presence) => {
        return sum + (presence.retardtotal || 0);
      }, 0);

      const baseSalary = user.salaire || 0;
      const salaryDeduction = calculateSalaryDeduction(totalRetardMinutes, baseSalary);

      // Group retard by days for detailed breakdown
      const retardByDays = (user.Presences || []).map(presence => ({
        date: moment(presence.date).format("YYYY-MM-DD"),
        retardMinutes: presence.retardtotal || 0,
        retardFormatted: formatMinutes(presence.retardtotal || 0)
      })).filter(day => day.retardMinutes > 0);

      return {
        id: user.id,
        name: user.name,
        email: user.email,
        baseSalary,
        totalRetardMinutes,
        totalRetardFormatted: formatMinutes(totalRetardMinutes),
        deductedHours: salaryDeduction.deductedHours,
        deductedAmount: salaryDeduction.deductedAmount,
        remainingSalary: salaryDeduction.remainingSalary,
        hourlyRate: salaryDeduction.hourlyRate,
        retardByDays,
        month: selectedMonth,
        year: selectedYear
      };
    });

    // Prepare response
    res.status(200).json({
      totalRecords: count,
      totalPages: limit === -1 ? 1 : Math.ceil(count / limit),
      currentPage: page,
      records: salaryCalculations,
      month: selectedMonth,
      year: selectedYear
    });
  } catch (error) {
    console.error("Error calculating salary deductions: ", error);
    res.status(500).json({ error: "An error occurred while calculating salary deductions" });
  }
};
