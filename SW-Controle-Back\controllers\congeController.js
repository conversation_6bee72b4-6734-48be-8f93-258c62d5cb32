const { Conge, User,UserInfo ,Schedule,Penalite,Absence,Autorisation} = require('../models');
const { Op } = require('sequelize');
const moment = require('moment');

// Create a new conge
exports.createConge = async (req, res) => {
  try {
    const { startDate, endDate, UserId, raison } = req.body;

    // Validate required fields
    if (!startDate || !endDate || !UserId || !raison) {
      return res.status(400).json({ message: "Tous les champs sont obligatoires" });
    }

    // Validate date format and range
    const congeStart = moment(startDate);
    const congeEnd = moment(endDate);
    const today = moment().startOf('day');

    if (!congeStart.isValid() || !congeEnd.isValid()) {
      return res.status(400).json({ message: "Format de date invalide" });
    }

    if (congeStart.isBefore(today)) {
      return res.status(400).json({ message: "La date de début doit être égale ou postérieure à aujourd'hui" });
    }

    if (congeEnd.isBefore(congeStart)) {
      return res.status(400).json({ message: "La date de fin doit être égale ou postérieure à la date de début" });
    }

    // Check for overlaps
    if (await hasOverlap(UserId, congeStart, congeEnd)) {
      return res.status(400).json({ message: "La demande de congé chevauche avec une pénalité, absence ou autorisation existante." });
    }

    if (await hasCongeOverlap(UserId, congeStart, congeEnd, null)) {
      return res.status(400).json({ message: "La demande de congé chevauche avec un autre congé existant pour cet utilisateur." });
    }

    // Calculate working days
    const nbrDeJour = calculateWorkingDays(congeStart, congeEnd);
    if (nbrDeJour <= 0) {
      return res.status(400).json({ message: "La durée du congé doit être d'au moins un jour" });
    }

    // Get current schedule
    const schedule = await Schedule.findOne({ 
      where: { isSelected: true }, 
      attributes: ['id'] 
    });

    if (!schedule) {
      return res.status(400).json({ message: "Aucun horaire n'est sélectionné" });
    }

    // Create the conge
    const reference = `C-${moment().format('DDMMYYYYHHmmss')}`;
    const conge = await Conge.create({
      startDate: congeStart.format('YYYY-MM-DD'),
      endDate: congeEnd.format('YYYY-MM-DD'),
      reference,
      nbrDeJour,
      status: 'en attente',
      UserId,
      raison,
      ScheduleId: schedule.id
    });

    res.status(201).json({ 
      message: 'Congé ajouté avec succès',
      conge: {
        id: conge.id,
        reference: conge.reference,
        startDate: conge.startDate,
        endDate: conge.endDate,
        nbrDeJour: conge.nbrDeJour,
        status: conge.status,
        raison: conge.raison
      }
    });
  } catch (error) {
    console.error('Error creating conge:', error);
    res.status(500).json({ message: "Erreur lors de la création du congé" });
  }
};
exports.getAllConges = async (req, res) => {
  try {
    const { page, limit, sortBy, sortOrder, UserId, date } = req.query;

    // Get current year and define start and end of the year
    const currentYear = new Date().getFullYear();
    const startOfYear = new Date(currentYear, 0, 1); // January 1st
    const endOfYear = new Date(currentYear, 11, 31); // December 31st

    // Parse filter date if provided
    let filterDate;
    if (date) {
      filterDate = new Date(date);
      if (isNaN(filterDate)) {
        return res.status(400).json({ message: 'Invalid date format.' });
      }
    }

    // Build where clause based on filters
    let whereClause = {};

    if (filterDate) {
      // Filter conges where the filter date falls within startDate and endDate
      whereClause = {
        startDate: { [Op.lte]: filterDate },
        endDate: { [Op.gte]: filterDate }
      };
    } else {
      // Default filter: conges within the current year
      whereClause = {
        startDate: { [Op.gte]: startOfYear },
        endDate: { [Op.lte]: endOfYear }
      };
    }

    // Add UserId filter if provided
    if (UserId && !isNaN(parseInt(UserId, 10))) {
      whereClause.UserId = UserId;
    }

    // Construct order based on sortBy and sortOrder
    let order = [];
    if (sortBy === 'User.name') {
      order = [[{ model: User, as: 'User' }, 'name', sortOrder || 'ASC']];
    } else {
      order = [[sortBy, sortOrder || 'ASC']];
    }

    // Construct query options
    const queryOptions = {
      where: whereClause,
      include: [
        {
          model: User,
          as: 'User',
          attributes: ['id', 'name']
        }
      ],
      order: order
    };

    // Handle pagination if page and limit are provided
    const pageInt = parseInt(page);
    const limitInt = parseInt(limit);

    if (!isNaN(pageInt) && !isNaN(limitInt) && limitInt > 0) {
      const offset = (pageInt - 1) * limitInt;
      queryOptions.limit = limitInt;
      queryOptions.offset = offset;
    }

    // Fetch conges with filtering, pagination, and sorting
    const conges = await Conge.findAndCountAll(queryOptions);

    // Calculate pagination details
    const totalItems = conges.count;
    const totalPages = limitInt > 0 ? Math.ceil(totalItems / limitInt) : 1;
    const currentPage = pageInt > 0 ? pageInt : 1;

    // Send response
    res.json({
      conges: conges.rows,
      totalPages,
      currentPage,
      totalItems
    });
  } catch (error) {
    console.error('Error in getAllConges:', error);
    res.status(500).json({ message: error.message });
  }
};

// Update a conge
exports.updateConge = async (req, res) => {
  try {
    const { startDate, endDate, status, raison } = req.body;
    const conge = await Conge.findByPk(req.params.id);
    if (!conge) {
      return res.status(404).json({ message: 'Conge not found' });
    }

    const congeStart = moment(startDate);
    const congeEnd = moment(endDate);

    if (await hasOverlap(conge.UserId, congeStart, congeEnd)) {
      return res.status(400).json({ message: "La demande de congé chevauche avec une pénalité, absence ou autorisation existante." });
    }
    if (await hasCongeOverlap(conge.UserId, congeStart, congeEnd, conge.id)) {
      return res.status(400).json({ message: "La demande de congé chevauche avec un autre congé existant pour vous." });
    }

    const nbrDeJour = calculateWorkingDays(congeStart, congeEnd);
    await conge.update({ startDate, endDate, status, raison, nbrDeJour });
    res.json({ message: 'Conge modifié avec succès' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete a conge
exports.deleteConge = async (req, res) => {
  try {
    const conge = await Conge.findByPk(req.params.id);
    if (conge) {
      await conge.destroy();
      res.json({ message: 'Conge supprimé avec succès' });
    } else {
      res.status(404).json({ message: 'Conge not found' });
    }
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get conges for a specific user
exports.getUserConges = async (req, res) => {
  try {
    const UserId = req.query.UserId;
    const page = parseInt(req.query.page); 
    const limit = parseInt(req.query.limit) ; 
    const sortBy = req.query.sortBy;
    const sortOrder = req.query.sortOrder; 

    if (!UserId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const offset = (page - 1) * limit; // Calculate offset for pagination

    const conges = await Conge.findAndCountAll({
      where: { UserId },
      include: [{ model: User, as: 'User', attributes: ['id', 'name'] }],
      order: [[sortBy, sortOrder]], 
      limit: limit, 
      offset: offset, 
    });

    res.json({
      conges: conges.rows,
      totalPages: Math.ceil(conges.count / limit),
      currentPage: page,
      totalItems: conges.count
    });
  } catch (error) {
    console.error('Error in getUserConges:', error);
    res.status(500).json({ message: error.message });
  }
};
exports.toggleCongeStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { newStatus } = req.body;

    if (!['accepté', 'en attente', 'rejeté'].includes(newStatus))  {
      return res.status(400).json({ message: 'Invalid status. Must be "en attente", "approuvé", or "rejeté".' });
    }

    const conge = await Conge.findByPk(id);

    if (!conge) {
      return res.status(404).json({ message: 'Conge not found' });
    }

    await conge.update({ status: newStatus });

    res.json({ message: 'Conge status updated successfully' });
  } catch (error) {
    console.error('Error in toggleCongeStatus:', error);
    res.status(500).json({ message: error.message });
  }
};
exports.getUserPenalites = async (req, res) => {
  try {
    const UserId = req.query.UserId;
    if (!UserId) {
      return res.status(400).json({ message: 'User ID is required' });
    }
    const penalites = await Penalite.findAll({ where: { UserId } });
    res.json(penalites);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
exports.checkCongeStatus = async (req, res) => {
  try {
    const id = req.query.id;
    if (!id) {
      return res.status(400).json({ message: 'Conge ID is required' });
    }

    const conge = await Conge.findByPk(id);

    if (!conge) {
      return res.status(404).json({ message: 'Conge not found' });
    }

    const isStatusDifferent = conge.status !== 'en attente';
    res.json({ result: isStatusDifferent });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
function datesOverlap(congeStart, congeEnd, otherStart, otherEnd) {
  return !(congeEnd.isBefore(otherStart) || congeStart.isAfter(otherEnd));
}
async function hasOverlap(UserId, congeStart, congeEnd) {
  // Fetch penalites for the user
  const penalites = await Penalite.findAll({ where: { UserId } });
  for (const penalite of penalites) {
    const pStart = moment(penalite.startDate);
    const pEnd = moment(penalite.endDate);
    if (datesOverlap(congeStart, congeEnd, pStart, pEnd)) {
      return true;
    }
  }

  // Fetch absences for the user
  const absences = await Absence.findAll({ where: { UserId } });
  for (const absence of absences) {
    const aDate = moment(absence.date);
    if (datesOverlap(congeStart, congeEnd, aDate, aDate)) {
      return true;
    }
  }

  // Fetch accepted autorisations for the user
  const autorisations = await Autorisation.findAll({ where: { UserId, status: 'accepté' } });
  for (const autorisation of autorisations) {
    const aDate = moment(autorisation.date);
    if (datesOverlap(congeStart, congeEnd, aDate, aDate)) {
      return true;
    }
  }

  return false;
}
async function hasCongeOverlap(UserId, congeStart, congeEnd, congeIdToExclude) {
  const overlappingConges = await Conge.findAll({
    where: {
      UserId,
      id: {
        [Op.ne]: congeIdToExclude // Exclude the current conge if updating
      },
      startDate: {
        [Op.lte]: congeEnd
      },
      endDate: {
        [Op.gte]: congeStart
      }
    }
  });
  return overlappingConges.length > 0;
}
function calculateWorkingDays(startDate, endDate) {
  let count = 0;
  let currentDate = moment(startDate);
  const end = moment(endDate);

  while (currentDate <= end) {
    if (currentDate.day() !== 0 && currentDate.day() !== 6) { // 0: Sunday, 6: Saturday
      count++;
    }
    currentDate.add(1, 'days');
  }

  return count;
}
async function hasCongeOverlap(UserId, congeStart, congeEnd, congeIdToExclude) {
  const overlappingConges = await Conge.findAll({
    where: {
      UserId,
      id: {
        [Op.ne]: congeIdToExclude // Exclude the current conge if updating
      },
      startDate: {
        [Op.lte]: congeEnd
      },
      endDate: {
        [Op.gte]: congeStart
      }
    }
  });
  return overlappingConges.length > 0;
}
