name: Deploy to AWS S3

on:
  push:
    branches:
      - main 
  workflow_dispatch:

jobs:
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Create .env file
        run: |
            touch .env
            echo "${{ secrets.ENVIRONEMENT_V }}" >> .env
            cat .env

      - name: Install dependencies
        run: npm i

      - name: Build application
        run: npm run build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: Deploy to S3
        run: aws s3 sync dist/ s3://$S3_BUCKET_NAME --delete
        env:
          S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
