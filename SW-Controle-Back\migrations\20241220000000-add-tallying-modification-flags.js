'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add the new boolean columns to track tallying modifications
    await queryInterface.addColumn('presences', 'entreeModified', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false
    });

    await queryInterface.addColumn('presences', 'sortieModified', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false
    });

    await queryInterface.addColumn('presences', 'entree1Modified', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false
    });

    await queryInterface.addColumn('presences', 'sortie1Modified', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove the columns if we need to rollback
    await queryInterface.removeColumn('presences', 'entreeModified');
    await queryInterface.removeColumn('presences', 'sortieModified');
    await queryInterface.removeColumn('presences', 'entree1Modified');
    await queryInterface.removeColumn('presences', 'sortie1Modified');
  }
};
