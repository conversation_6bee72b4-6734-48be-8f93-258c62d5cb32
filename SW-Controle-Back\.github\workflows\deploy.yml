name: Build and Deploy

on:
  push:
    branches:
      - main  
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy on remote server
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key
          chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i private_key ubuntu@*********** << 'EOF'
           ./deploy-sw.sh
          EOF