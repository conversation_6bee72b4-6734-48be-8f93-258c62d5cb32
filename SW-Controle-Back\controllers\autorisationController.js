const { Autorisation, User, Schedule, Absence, Penalite, Conge } = require('../models');
const moment = require('moment');
const { Op } = require('sequelize');

exports.createAutorisation = async (req, res) => {
  try {
    const { UserId, date, heureDebut, heureFin } = req.body;
    const schedule = await Schedule.findOne({ where: { isSelected: true }, attributes: ['id'] });
    // Check for existing autorisation with overlapping time slots on the same day
    const existingAutorisation = await Autorisation.findOne({
      where: {
        UserId: UserId,
        date: date,
        [Op.and]: [
          { heureDebut: { [Op.lt]: heureFin } },
          { heureFin: { [Op.gt]: heureDebut } }
        ]
      }
    });
    if (existingAutorisation) {
      return res.status(400).json({ message: 'Une autorisation chevauche avec un horaire existant pour cette journée.' });
    }

    // Check for penalties on the same day
    const penalty = await Penalite.findOne({
      where: {
        UserId: UserId,
        startDate: {
          [Op.lte]: date
        },
        endDate: {
          [Op.gte]: date
        }
      }
    });
    if (penalty) {
      return res.status(400).json({ message: 'vous avez une pénalité pour cette journée.' });
    }

    // Check for absences on the same day
    const absence = await Absence.findOne({
      where: {
        UserId: UserId,
        date: date
      }
    });
    if (absence) {
      return res.status(400).json({ message: 'vous avez une absence pour cette journée.' });
    }

    // Check for conges that include this date
    const conge = await Conge.findOne({
      where: {
        UserId: UserId,
        startDate: {
          [Op.lte]: date
        },
        endDate: {
          [Op.gte]: date
        },
        status:'accepté'
      }
    });
    if (conge) {
      return res.status(400).json({ message: 'L\'utilisateur est en congé pour cette journée.' });
    }

    // Proceed to create autorisation
    const lastAutorisation = await Autorisation.findOne({
      order: [['id', 'DESC']]
    });
    const autorisationId = lastAutorisation ? lastAutorisation.id + 1 : 1;
    const currentDate = moment().format('DD-MM-YYYY');
    const référence = `A-${autorisationId}-${currentDate}`;
    const calculateNbrHeure = (startTime, endTime) => {
      const start = moment(startTime, 'HH:mm');
      const end = moment(endTime, 'HH:mm');
      return end.diff(start, 'minutes');
    };
    const nbrheures = calculateNbrHeure(heureDebut, heureFin);
    await Autorisation.create({
      référence,
      UserId: UserId,
      date: date,
      heureDebut: heureDebut,
      heureFin: heureFin,
      nbrheures: nbrheures,
      status: 'en attente',
      ScheduleId: schedule.id // Ensure schedule is fetched correctly
    });

    res.status(201).json({ message: 'Autorisation ajoutée avec succès' });
  } catch (error) {
    res.status(500).json({ message:'Une erreur est survenue. Veuillez réessayer plus tard.'});
  }
};

// Update an autorisation
exports.updateAutorisation = async (req, res) => {
  try {
    const autorisationId = req.params.id;
    const { UserId, date, heureDebut, heureFin } = req.body;

    // Fetch the autorisation to update
    const autorisation = await Autorisation.findByPk(autorisationId);
    if (!autorisation) {
      return res.status(404).json({ message: 'Autorisation non trouvée.' });
    }

    // Check for existing autorisation with overlapping time slots on the same day, excluding the current autorisation
    const existingAutorisation = await Autorisation.findOne({
      where: {
        UserId: UserId,
        date: date,
        [Op.and]: [
          { heureDebut: { [Op.lt]: heureFin } },
          { heureFin: { [Op.gt]: heureDebut } }
        ],
        id: { [Op.ne]: autorisationId },
      }
    });
    if (existingAutorisation) {
      return res.status(400).json({ message: 'Une autorisation chevauche avec un horaire existant pour cette journée.' });
    }

    // Check for penalties on the same day
    const penalty = await Penalite.findOne({
      where: {
        UserId: UserId,
        startDate: {
          [Op.lte]: date
        },
        endDate: {
          [Op.gte]: date
        }
      }
    });
    if (penalty) {
      return res.status(400).json({ message: 'vous avez une pénalité pour cette journée.' });
    }

    // Check for absences on the same day
    const absence = await Absence.findOne({
      where: {
        UserId: UserId,
        date: date
      }
    });
    if (absence) {
      return res.status(400).json({ message: 'vous avez une absence pour cette journée.' });
    }

    // Check for conges that include this date
    const conge = await Conge.findOne({
      where: {
        UserId: UserId,
        startDate: {
          [Op.lte]: date
        },
        endDate: {
          [Op.gte]: date
        },
        status:'accepté'
      }
    });
    if (conge) {
      return res.status(400).json({ message: 'vous avez un congé pour cette journée.' });
    }

    // Update the autorisation
    const calculateNbrHeure = (startTime, endTime) => {
      const start = moment(startTime, 'HH:mm');
      const end = moment(endTime, 'HH:mm');
      return end.diff(start, 'minutes');
    };
    const nbrheures = calculateNbrHeure(heureDebut, heureFin);
    await autorisation.update({
      UserId: UserId,
      date: date,
      heureDebut: heureDebut,
      heureFin: heureFin,
      nbrheures: nbrheures
    });

    res.json({ message: 'Autorisation mise à jour avec succès' });
  } catch (error) {
    res.status(500).json({ message: 'Une erreur est survenue. Veuillez réessayer plus tard.' });
  }
};
exports.getAllAutorisations = async (req, res) => {
  try {
    const { page, limit, sortBy, sortOrder, agentId, date } = req.query;
    const whereClause = {};

    // Filter by agentId if provided and valid
    if (agentId && !isNaN(parseInt(agentId, 10))) {
      whereClause.UserId = parseInt(agentId, 10);
    }

    // Filter by date if provided and valid
    if (date) {
      const parsedDate = moment(date, 'YYYY-MM-DD', true).utc();
      if (parsedDate.isValid()) {
        whereClause.date = parsedDate.format('YYYY-MM-DD');
      } else {
        return res.status(400).json({ message: 'Invalid date format. Please use YYYY-MM-DD.' });
      }
    } else {
      // Default filter: current year
      const startOfYear = moment().startOf('year').utc().format('YYYY-MM-DD');
      const endOfYear = moment().endOf('year').utc().format('YYYY-MM-DD');
      whereClause.date = {
        [Op.gte]: startOfYear,
        [Op.lte]: endOfYear
      };
    }

    // Construct the order array based on sortBy and sortOrder
    let order = [];
    if (sortBy === 'User.name') {
      order = [[{ model: User, as: 'User' }, 'name', sortOrder]];
    } else {
      order = [[sortBy, sortOrder]];
    }

    // Construct query options
    const queryOptions = {
      where: whereClause,
      include: [{
        model: User,
        as: 'User',
        attributes: ['id', 'name']
      }],
      order: order
    };

    // Handle pagination if both page and limit are valid
    const pageInt = parseInt(page);
    const limitInt = parseInt(limit);

    if (!isNaN(pageInt) && !isNaN(limitInt) && limitInt > 0) {
      const offset = (pageInt - 1) * limitInt;
      queryOptions.limit = limitInt;
      queryOptions.offset = offset;
    }

    // Fetch autorisations with filtering, pagination, and sorting
    const autorisations = await Autorisation.findAndCountAll(queryOptions);

    // Calculate pagination details
    const totalItems = autorisations.count;
    const totalPages = limitInt > 0 ? Math.ceil(totalItems / limitInt) : 1;
    const currentPage = pageInt > 0 ? pageInt : 1;

    // Send response
    res.json({
      autorisations: autorisations.rows,
      totalPages,
      currentPage,
      totalItems
    });
  } catch (error) {
    console.error('Error in getAllAutorisations:', error);
    res.status(500).json({ message: error.message, stack: error.stack });
  }
};

// Get a single autorisation by id

// Delete an autorisation
exports.deleteAutorisation = async (req, res) => {
  try {
    const autorisation = await Autorisation.findByPk(req.params.id);
    if (autorisation) {
      await autorisation.destroy();
      res.json({ message: 'Autorisation supprimée avec succès' });
    } else {
      res.status(404).json({ message: 'Autorisation not found' });
    }
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get autorisations for a specific user
exports.getUserAutorisations = async (req, res) => {
  try {
    const UserId = req.query.UserId;
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const sortBy = req.query.sortBy;
    const sortOrder = req.query.sortOrder;
    const offset = (page - 1) * limit;

    if (!UserId) {
      return res.status(400).json({ message: 'User ID is required' });
    }
    const autorisations = await Autorisation.findAndCountAll({
      where: { UserId: UserId },
      include: [{ model: User, as: 'User', attributes: ['id', 'name'] }],
      order: [[sortBy, sortOrder]],
      limit: limit,
      offset: offset,
    });

    res.json({
      autorisations: autorisations.rows,
      totalPages: Math.ceil(autorisations.count / limit),
      currentPage: page,
      totalItems: autorisations.count
    });
  } catch (error) {
    console.error('Error in getUserAutorisations:', error);
    res.status(500).json({ message: error.message });
  }
};

// Toggle autorisation status
exports.toggleAutorisationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { newStatus } = req.body;
    if (!['accepté', 'en attente', 'rejeté'].includes(newStatus)) {
      return res.status(400).json({ message: 'Invalid status. Must be "accepté", "en attente", or "rejeté".' });
    }

    const autorisation = await Autorisation.findByPk(id);

    if (!autorisation) {
      return res.status(404).json({ message: 'Autorisation not found' });
    }

    await autorisation.update({ status: newStatus });

    res.json({ message: 'Autorisation status updated successfully' });
  } catch (error) {
    console.error('Error in toggleAutorisationStatus:', error);
    res.status(500).json({ message: error.message });
  }
};
exports.checkAutorisationStatus = async (req, res) => {
  try {
    const id = req.query.id;
    if (!id) {
      return res.status(400).json({ message: 'autorisation ID is required' });
    }

    const conge = await Autorisation.findByPk(id);

    if (!conge) {
      return res.status(404).json({ message: 'autorisation not found' });
    }

    const isStatusDifferent = conge.status !== 'en attente';
    res.json({ result: isStatusDifferent });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};