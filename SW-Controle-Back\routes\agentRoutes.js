const express = require('express');
const router = express.Router();
const agentController = require('../controllers/agentController');
const { verifyToken, authorizeRoles } = require('../middleware/authMiddleware');

// Get all agents (admin only)
router.get('/', verifyToken, authorizeR<PERSON>s('admin'), agentController.getAgents);

// Create a new agent (admin only)
router.post('/', verifyToken, authorize<PERSON><PERSON><PERSON>('admin'), agentController.createAgent);

// Update an agent (admin only)
router.put('/:id', verifyToken, authorizeRoles('admin'), agentController.updateAgent);

// Delete an agent (admin only)
router.delete('/:id', verifyToken, authorizeRoles('admin'), agentController.deleteAgent);

// Get agent information (admin only)
router.get('/userinfo', verifyToken, authorizeR<PERSON>s('employe'), agentController.AgentInfo);

// Get all employee users (admin only)
router.get('/allagents', verifyToken, authorizeR<PERSON>s('admin'), agentController.getEmployeUsers);

module.exports = router;
