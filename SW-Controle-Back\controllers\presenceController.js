const { User, Penalite, Absence, Autorisation, Presence, Conge, UserInfo,Schedule} = require('../models');
const moment = require('moment');
const { Sequelize,Op } = require('sequelize');
require('dotenv').config();
const sequelize = new Sequelize(process.env.DB_DATABASE,process.env.DB_USERNAME,process.env.DB_PASSWORD, {
  host:process.env.DB_HOST,
  dialect:process.env.DB_DIALECT
});

const presenceController = {
  async getPresenceAndAbsence(req, res) {
    try {
      const { dateselect, page, itemsPerPage } = req.query;
  
      if (!dateselect) {
        return res.status(400).json({ error: 'Date parameter is required' });
      }
  
      const startOfDay = moment(dateselect).startOf('day').format('YYYY-MM-DD');
      const endOfDay = moment(dateselect).endOf('day').format('YYYY-MM-DD');
  
      const users = await User.findAll({
        attributes: ['id', 'name'],
        where: { role: 'employe' },
        include: [
          {
            model: Presence,
            required: false,
            where: { date: startOfDay },
          },
          {
            model: Absence,
            required: false,
            where: { date: startOfDay },
          },
          {
            model: Penalite,
            required: false,
            where: {
              startDate: { [Op.lte]: endOfDay },
              endDate: { [Op.gte]: startOfDay }
            },
          },
          {
            model: Autorisation,
            required: false,
            where: { date: startOfDay },
          },
          {
            model: Conge,
            required: false,
            where: {
              startDate: { [Op.lte]: dateselect },
              endDate: { [Op.gte]: dateselect }
            }
          }
        ]
      });
  
      let result = users.map((user) => {
        const presence = user.Presences[0];
        const absence = user.Absences[0];
        const penalite = user.Penalites[0];
        const autorisation = user.Autorisations[0];
        const conge = user.Conges[0];
  
        let recordType = 'N/A';
        let recordId = null;
        let isPresent = 'N/A';
        let commentaires = 'N/A';
  
        if (presence) {
          recordType = 'presence';
          recordId = presence.id;
          isPresent = 'Présent';
          commentaires = `presence:${presence.commentaires || 'N/A'}`;
        } else if (absence) {
          recordType = 'absence';
          recordId = absence.id;
          isPresent = 'Absent';
          commentaires = `absence:${absence.raison || 'N/A'}`;
        } else if (penalite) {
          recordType = 'penalite';
          recordId = penalite.id;
          commentaires = `penalite:${penalite.raison || 'N/A'}`;
        } else if (autorisation) {
          recordType = 'autorisation';
          recordId = autorisation.id;
          commentaires = `autorisation:${autorisation.status || 'N/A'}`;
        } else if (conge) {
          recordType = 'conge';
          recordId = conge.id;
          commentaires = `conge:${conge.raison || 'N/A'}`;
        }
  
        return {
          recordType,
          recordId,
          UserId: user.id,
          Agent: user.name,
          environnement: presence ? presence.environnement : 'N/A',
          absence: isPresent,
          entree: presence ? presence.entree : 'N/A',
          sortie: presence ? presence.sortie : 'N/A',
          entree1: presence ? presence.entree1 : 'N/A',
          sortie1: presence ? presence.sortie1 : 'N/A',
          prod: presence ? presence.prod : 'N/A',
          prodMatin: presence ? presence.prodMatin : 'N/A',
          prodApresMidi: presence ? presence.prodApresMidi : 'N/A',
          commentaires: commentaires,
        };
      });
  
      const totalItems = result.length;
  
      res.json({
        data: result,
        pagination: {
          totalItems,
        }
      });
    } catch (error) {
      console.error(error);
      res.status(500).json(error);
    }
  },

 

  async updatePresence(req, res) {
    const { id, entree, sortie, entree1, sortie1, userId } = req.body;
    const schedule = await Schedule.findOne({ where: { isSelected: true } });
    const moment = require('moment-timezone');

    if (!id) {
        return res.status(400).json({ error: 'ID is required' });
    }

    try {
        function calculateProductionHours(entree, sortie, entree1, sortie1, autorisationMinutes = 0) {
            let morningMinutes = 0;
            if (entree && sortie) {
                const morningStart = moment.tz(entree, 'HH:mm:ss', 'Africa/Tunis');
                const morningEnd = moment.tz(sortie, 'HH:mm:ss', 'Africa/Tunis');
                morningMinutes = morningEnd.diff(morningStart, 'minutes');
            }

            let afternoonMinutes = 0;
            if (entree1 && sortie1) {
                const afternoonStart = moment.tz(entree1, 'HH:mm:ss', 'Africa/Tunis');
                const afternoonEnd = moment.tz(sortie1, 'HH:mm:ss', 'Africa/Tunis');
                afternoonMinutes = afternoonEnd.diff(afternoonStart, 'minutes');
            }

            const totalMinutes = morningMinutes + afternoonMinutes;
            const remainingAuth = Math.max(autorisationMinutes - morningMinutes, 0);
            
            return {
                prod: Math.round(totalMinutes),
                prodMatin: Math.round(Math.max(morningMinutes - autorisationMinutes, 0)),
                prodApresMidi: Math.round(Math.max(afternoonMinutes - remainingAuth, 0)),
            };
        }

        // First, get the current presence record to check modification flags
        const currentPresence = await Presence.findOne({ where: { id } });
        if (!currentPresence) {
            return res.status(404).json({ error: 'Presence record not found' });
        }

        // Check if any of the requested time fields have already been modified
        // Handle cases where the new boolean fields might not exist in production DB yet
        if (entree && currentPresence.entreeModified === true) {
            return res.status(400).json({ error: 'Not allowed - Morning entry time has already been tallied' });
        }
        if (sortie && currentPresence.sortieModified === true) {
            return res.status(400).json({ error: 'Not allowed - Morning exit time has already been tallied' });
        }
        if (entree1 && currentPresence.entree1Modified === true) {
            return res.status(400).json({ error: 'Not allowed - Afternoon entry time has already been tallied' });
        }
        if (sortie1 && currentPresence.sortie1Modified === true) {
            return res.status(400).json({ error: 'Not allowed - Afternoon exit time has already been tallied' });
        }

        const tunisTime = moment().tz('Africa/Tunis');
        const timeString = tunisTime.format('HH:mm:ss');

        const updateData = {};
        if (entree) {
            updateData.entree = timeString;
            // Only set the modified flag if the column exists
            if (currentPresence.hasOwnProperty('entreeModified')) {
                updateData.entreeModified = true;
            }
        }
        if (sortie) {
            updateData.sortie = timeString;
            if (currentPresence.hasOwnProperty('sortieModified')) {
                updateData.sortieModified = true;
            }
        }
        if (entree1) {
            updateData.entree1 = timeString;
            if (currentPresence.hasOwnProperty('entree1Modified')) {
                updateData.entree1Modified = true;
            }
        }
        if (sortie1) {
            updateData.sortie1 = timeString;
            if (currentPresence.hasOwnProperty('sortie1Modified')) {
                updateData.sortie1Modified = true;
            }
        }

        const [updated] = await Presence.update(updateData, { where: { id } });
        if (!updated) return res.status(404).json({ error: 'Presence record not found' });

        const updatedPresence = await Presence.findOne({ where: { id } });
        const presenceDate = updatedPresence.date;

        const autorisations = await Autorisation.findAll({
            where: {
                UserId: updatedPresence.UserId,
                date: presenceDate,
                status: 'accepté'
            },
        });

        let totalAutorisationMinutes = autorisations.reduce((acc, curr) => acc + (curr.nbrheures || 0), 0);

        if (schedule && schedule.isRecurring) {
            let retardm = 0;
            const entreeTime = moment.tz(updatedPresence.entree, 'HH:mm:ss', 'Africa/Tunis');
            const startTimeM = moment.tz('09:00', 'HH:mm', 'Africa/Tunis');
            if (entreeTime.isAfter(startTimeM)) {
                retardm += entreeTime.diff(startTimeM, 'minutes');
            }

            const sortieTime = moment.tz(updatedPresence.sortie, 'HH:mm:ss', 'Africa/Tunis');
            const endTimeM = moment.tz('13:00', 'HH:mm', 'Africa/Tunis');
            if (sortieTime.isBefore(endTimeM)) {
                retardm += endTimeM.diff(sortieTime, 'minutes');
            }

            let retardam = 0;
            const entree1Time = moment.tz(updatedPresence.entree1, 'HH:mm:ss', 'Africa/Tunis');
            const startTimeAM = moment.tz('14:00', 'HH:mm', 'Africa/Tunis');
            if (entree1Time.isAfter(startTimeAM)) {
                retardam += entree1Time.diff(startTimeAM, 'minutes');
            }

            const sortie1Time = moment.tz(updatedPresence.sortie1, 'HH:mm:ss', 'Africa/Tunis');
            const endTimeAM = moment.tz('18:00', 'HH:mm', 'Africa/Tunis');
            if (sortie1Time.isBefore(endTimeAM)) {
                retardam += endTimeAM.diff(sortie1Time, 'minutes');
            }

            const retardtotal = retardm + retardam;
            
            const { prod, prodMatin, prodApresMidi } = calculateProductionHours(
                updatedPresence.entree,
                updatedPresence.sortie,
                updatedPresence.entree1,
                updatedPresence.sortie1,
                totalAutorisationMinutes
            );

            await Presence.update({ 
                prod: prodMatin + prodApresMidi,
                prodMatin,
                prodApresMidi,
                retardm,
                retardam,
                retardtotal
            }, { where: { id } });

        } else {
            let retardm = 0;
            const entreeTime = moment.tz(updatedPresence.entree, 'HH:mm:ss', 'Africa/Tunis');
            const startTimeM = moment.tz(schedule.morningStart, 'HH:mm:ss', 'Africa/Tunis');
            if (entreeTime.isAfter(startTimeM)) {
                retardm += entreeTime.diff(startTimeM, 'minutes');
            }

            const sortieTime = moment.tz(updatedPresence.sortie, 'HH:mm:ss', 'Africa/Tunis');
            const endTimeM = moment.tz(schedule.morningEnd, 'HH:mm:ss', 'Africa/Tunis');
            if (sortieTime.isBefore(endTimeM)) {
                retardm += endTimeM.diff(sortieTime, 'minutes');
            }

            const { prodMatin } = calculateProductionHours(
                updatedPresence.entree,
                updatedPresence.sortie,
                undefined,
                undefined,
                totalAutorisationMinutes
            );

            await Presence.update({ 
                prod: prodMatin,
                prodMatin,
                retardm,
                retardtotal: retardm
            }, { where: { id } });
        }

        return res.status(200).json({ message: 'Presence updated successfully' });
    } catch (error) {
        console.error(error);
        return res.status(500).json({ error: error.message });
    }
},
  async addPointage(req, res) {
    try {
      const { env, date, status, UserId } = req.query;
      const result = await Presence.create({
        environnement: env,
        date: date,
        status: status,
        UserId: UserId,
       
      });
      res.json(result.id);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  },

  async getPresences(req, res) {
    const today = new Date().toISOString().slice(0, 10);
    const agentId = req.query.agentId;

    // Define the base query options
    const queryOptions = {
        attributes: ['name', 'id'],
        where: {
            role: 'employe',
        },
        include: [
            {
                model: Presence,
                required: false,
                where: {
                    date: today
                }
            },
            {
                model: Absence,
                required: false,
                where: {
                    date: today
                }
            },
            {
              model: Penalite,
              required: false,
              where: {
                startDate: { [Op.lte]: today },
                endDate: { [Op.gte]: today}
              },
            },
            {
              model: Conge,
              required: false,
              where: {
                startDate: { [Op.lte]: today },
                endDate: { [Op.gte]: today }
              }
            }
        ],
    };

    // If agentId is provided, add it to the where clause
    if (agentId) {
        queryOptions.where.Id = parseInt(agentId);
    }

    const result = await User.findAll(queryOptions);

    const formattedData = result.flatMap(user => {
        // Check if there is any absence record for today
        if (user.Absences && user.Absences.length > 0) {
            return [{
                User: user.name,
                date: today,
                environnement: 'absent',
                overallStatus: 'absent',
                // Set all other fields to 'N/A'
                entree: 'N/A',
                sortie: 'N/A',
                entree1: 'N/A',
                sortie1: 'N/A',
                prod: 'N/A',
                prodm: 'N/A',
                prodam: 'N/A',
                retardtotal: 'N/A',
                retardm: 'N/A',
                retardam: 'N/A',
                status: 'N/A',
                UserId: user.id
            }];
        } 
        if (user.Penalites && user.Penalites.length > 0) {
          return [{
              User: user.name,
              date: today,
              environnement: 'penalite',
              overallStatus: 'penalite',
              // Set all other fields to 'N/A'
              entree: 'N/A',
              sortie: 'N/A',
              entree1: 'N/A',
              sortie1: 'N/A',
              prod: 'N/A',
              prodm: 'N/A',
              prodam: 'N/A',
              retardtotal: 'N/A',
              retardm: 'N/A',
              retardam: 'N/A',
              status: 'N/A',
              UserId: user.id
          }];
      } 
      if (user.Conges && user.Conges.length > 0) {
        return [{
            User: user.name,
            date: today,
            environnement: 'conge',
            overallStatus: 'conge',
            // Set all other fields to 'N/A'
            entree: 'N/A',
            sortie: 'N/A',
            entree1: 'N/A',
            sortie1: 'N/A',
            prod: 'N/A',
            prodm: 'N/A',
            prodam: 'N/A',
            retardtotal: 'N/A',
            retardm: 'N/A',
            retardam: 'N/A',
            status: 'N/A',
            UserId: user.id
        }];
    }else if (!user.Presences || user.Presences.length === 0) {
            // No absence or presence record
            return [{
                User: user.name,
                date: today,
                environnement: 'N/A',
                overallStatus: 'N/A',
                // Set all other fields to 'N/A'
                entree: 'N/A',
                sortie: 'N/A',
                entree1: 'N/A',
                sortie1: 'N/A',
                prod: 'N/A',
                prodm: 'N/A',
                prodam: 'N/A',
                retardtotal: 'N/A',
                retardm: 'N/A',
                retardam: 'N/A',
                status: 'N/A',
                UserId: user.id
            }];
        } else {
            // Return presence data as before
            return user.Presences.map(presence => ({
                User: user.name,
                UserId: user.id,
                id: presence.id,
                date: presence.date,
                environnement: presence.environnement,
                entree: presence.entree,
                sortie: presence.sortie,
                entree1: presence.entree1,
                sortie1: presence.sortie1,
                prod: presence.prod,
                prodm: presence.prodMatin,
                prodam: presence.prodApresMidi,
                retardtotal: presence.retardtotal,
                retardm: presence.retardm,
                retardam: presence.retardam,
                morningEntryStatus: presence.morningEntryStatus,
                morningExitStatus: presence.morningExitStatus,
                breakEntryStatus: presence.breakEntryStatus,
                afternoonEntryStatus: presence.afternoonEntryStatus,
                afternoonExitStatus: presence.afternoonExitStatus,
                overallStatus: presence.overallStatus
            }));
        }
    });

    return res.json(formattedData);
},

async checkButtonStatus(req, res) {
  const { presenceId, buttonNumber } = req.query;
  
  if (!presenceId || !buttonNumber) {
    return res.status(400).json({ error: 'Presence ID and buttonNumber are required' });
  }

  const presence = await Presence.findByPk(presenceId);

  if (!presence) {
    return res.status(404).json({ error: 'Presence record not found' });
  }

  const statusFields = {
    1: 'morningEntryStatus',
    2: 'morningExitStatus',
    3: 'afternoonEntryStatus',
    4: 'afternoonExitStatus'
  };

  const field = statusFields[buttonNumber];

  if (!field) {
    return res.status(400).json({ error: 'Invalid buttonNumber' });
  }

  let disabled = false;

  switch (buttonNumber) {
    case '2':
      // Check if morningEntryStatus is accepted
      disabled = presence.morningEntryStatus == null;
      break;
    case '3':
      // Check if morningExitStatus is accepted
      disabled = presence.morningExitStatus == null;
      break;
    case '4':
      // Check if afternoonEntryStatus is accepted
      disabled = presence.afternoonEntryStatus == null;
      break;
    case '1':
      // Morning entry is never disabled
      disabled = false;
      break;
    default:
      disabled = true;
      break;
  }

  return res.json({ disabled });
},

async updatePresenceField(req, res) {
  const { id, field, status } = req.body;
  const presence = await Presence.findByPk(id);

  if (!presence) {
    return res.status(404).json({ error: 'Presence record not found' });
  }

  if (presence[field] !== null) {
    return res.status(400).json({ error: 'Field already confirmed' });
  }

  presence[field] = status === 'true';
  await presence.save();

  return res.status(200).json({
    message: 'Presence field updated successfully',
    updatedRows: 1,
  });
},
async updateAllStatuses(req, res) {
  const records = req.body;
  try {
    // Check if records is an array
    if (!Array.isArray(records)) {
      return res.status(400).json({ error: 'Records must be an array.' });
    }

    // Find the selected schedule
    const schedule = await Schedule.findOne({
      where: { isSelected: true },
      attributes: ['isRecurring'],
    });

    if (!schedule) {
      return res.status(400).json({ error: 'No schedule is selected.' });
    }

    // Determine fields to update
    let fieldsToUpdate;
    if (schedule.isRecurring) {
      fieldsToUpdate = [
        'morningEntryStatus',
        'morningExitStatus',
        'afternoonEntryStatus',
        'afternoonExitStatus',
        'overallStatus'
      ];
    } else {
      fieldsToUpdate = ['morningEntryStatus', 'morningExitStatus','overallStatus'];
    }

    let updatedCount = 0;
    for (const record of records) {
      // Ensure record has an id
      if (!record.id) {
        continue; // Skip records without an id
      }
      // Create update data with only the fields to update
      const updateData = {};
      fieldsToUpdate.forEach(field => {
        if (record.hasOwnProperty(field)) {
          updateData[field] = record[field];
        }
      });
      // Update the presence entry
      const updated = await Presence.update(updateData, {
        where: { id: record.id }
      });
      if (updated[0] > 0) {
        updatedCount++;
      }
    } 

    res.status(200).json({ message: 'All statuses updated successfully.' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'An error occurred while updating statuses.' });
  }
},

async checkCongeToday(req, res) {
  const today = moment().format('YYYY-MM-DD');
  const Id = parseInt(req.query.UserId, 10);

  const conge = await Conge.findOne({
    where: {
      UserId: Id,
      [Op.and]: [
        sequelize.where(sequelize.fn('DATE', sequelize.col('startDate')), { [Op.lte]: today }),
        sequelize.where(sequelize.fn('DATE', sequelize.col('endDate')), { [Op.gte]: today }),
      ],
      status: 'accepté'
    }
  });

  const bool = conge !== null;

  res.json({ hasConge: bool });
},
async checkPenaliteToday(req, res){
  const today = moment().format('YYYY-MM-DD');
  const Id = parseInt(req.query.UserId, 10);
  const penalite = await Penalite.findOne({
    where: {
      UserId: Id,
      [Op.and]: [
        sequelize.where(sequelize.fn('DATE', sequelize.col('startDate')), { [Op.lte]: today }),
        sequelize.where(sequelize.fn('DATE', sequelize.col('endDate')), { [Op.gte]: today }),
      ],
    }
  });
  const bool = penalite !== null;
  
  res.json({ hasPenalite: bool});
},
async getCurrentPresenceStatus(req, res) {
  const today = moment().format('YYYY-MM-DD');
  const userId = req.user.id; // Assuming user is authenticated

  const presence = await Presence.findOne({
    where: {
      UserId: userId,
      date: today
    },
    include: [
      {
        model: Schedule,
        attributes: ['isRecurring']
      }
    ]
  });

  if (!presence) {
    return res.json({
      presenceId: null,
      environmentSelected: false,
      isCompleted: false,
      buttonStatus: {
        morningExit: false,
        afternoonEntry: false,
        afternoonExit: false
      },
      scheduleType: null // or appropriate default
    });
  }

  const scheduleType =await Schedule.findOne({
    where: { isSelected: true },
    attributes: ['isRecurring'],
  });
  let currentButton = 1;
  let isCompleted = false;
  const buttonStatus = {
    morningExit: false,
    afternoonEntry: false,
    afternoonExit: false
  };

  if (scheduleType) {
    // Recurring schedule
    if (presence.entree && presence.sortie && presence.entree1 && presence.sortie1) {
      isCompleted = true;
    } else {
      if (presence.entree) currentButton = 2;
      if (presence.sortie) currentButton = 3;
      if (presence.entree1) currentButton = 4;
      if (presence.sortie1) isCompleted = true;

      buttonStatus.morningExit = !!presence.sortie;
      buttonStatus.afternoonEntry = !!presence.entree1;
      buttonStatus.afternoonExit = !!presence.sortie1;
    }
  } else {
    // Non-recurring schedule
    if (presence.entree && presence.sortie) {
      isCompleted = true;
    } else {
      if (presence.entree) currentButton = 2;
    }

    buttonStatus.morningExit = !!presence.sortie;
  }

  res.json({
    presenceId: presence.id,
    environmentSelected: !!presence.environnement,
    currentButton,
    isCompleted,
    buttonStatus,
    scheduleType
  });
},
async getCurrentDateTime(req, res)  {
  const currentDateTime = new Date().toISOString();
  res.json({ currentDateTime });
}

};



module.exports = presenceController;