import axios from 'axios';

const getAccessToken = () => localStorage.getItem('accessToken');

const state = {
  conges: [],
  totalPages: 0,
  currentPage: 1,
  totalItems: 0,
  loading: false,
  error: null,
  penalites: [],
};

const mutations = {
  SET_CONGES(state, { conges, totalPages, currentPage, totalItems }) {
    state.conges = conges;
    state.totalPages = totalPages;
    state.currentPage = currentPage;
    state.totalItems = totalItems;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  SET_ERROR(state, error) {
    state.error = error;
  },
  SET_PENALITES(state, penalites) {
    state.penalites = penalites;
  },
};

const actions = {
  async fetchUserPenalites({ commit,state}, UserId) {
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/penalitesconge`, {
        params: { UserId },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      commit('SET_PENALITES', response.data);
      console.log(state.penalites)
    } catch (error) {
      commit('SET_ERROR', error.message);
    }
  },
  async fetchConges({ commit,state }, { page, limit, sortBy, sortOrder, UserId,date }) {
    commit('SET_LOADING', true);
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/conges`, {
        params: { page, limit, sortBy, sortOrder, UserId,date },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      commit('SET_CONGES', response.data);
      console.log(state.conge)
    } catch (error) {
      commit('SET_ERROR', error.message);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async createConge({ commit }, congeData) {
    commit('SET_LOADING', true);
    try {
      await axios.post(`${process.env.VUE_APP_API_URL}api/conges`, congeData, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
    } catch (error) {
      commit('SET_ERROR', error.message);
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  async updateConge({ commit }, { id, congeData }) {
    commit('SET_LOADING', true);
    try {
      await axios.put(`${process.env.VUE_APP_API_URL}api/conges/${id}`, congeData, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
    } catch (error) {
      console.log(error.message)
      commit('SET_ERROR', error.message);
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async deleteConge({ commit }, id) {
    commit('SET_LOADING', true);
    try {
      await axios.delete(`${process.env.VUE_APP_API_URL}api/conges/${id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
    } catch (error) {
      commit('SET_ERROR', error.message);
    } finally {
      commit('SET_LOADING', false);
    }
  },
  async fetchUserConges({ commit }, { UserId, page, limit, sortBy, sortOrder }) {
    commit('SET_LOADING', true);
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/conge`, {
        params: { page, limit, sortBy, sortOrder ,UserId},
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      commit('SET_CONGES', response.data);
    } catch (error) {
      commit('SET_ERROR', error.message);
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  async toggleCongeStatus({ commit }, { id, newStatus }) {
    commit('SET_LOADING', true);
    try {
      const response = await axios.put(`${process.env.VUE_APP_API_URL}api/conge/${id}`, 
        { newStatus },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('accessToken')}`
          }
        }
      );
      console.log(response)
      // Optionally, you can update the local state here if needed
    } catch (error) {
      commit('SET_ERROR', error.message);
      throw error;
    } finally {
      commit('SET_LOADING', false);
    }
  },
};

const getters = {
  getConges: state => state.conges,
  getTotalPages: state => state.totalPages,
  getCurrentPage: state => state.currentPage,
  getTotalItems: state => state.totalItems,
  isLoading: state => state.loading,
  getError: state => state.error,
  
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};