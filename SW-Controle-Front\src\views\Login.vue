<template>
  <v-container fluid>
    <v-row justify="center">
      <v-col cols="12" sm="8" md="4">
        <v-card>
          <v-toolbar dark color="primary">
            <v-toolbar-title>SW Controle</v-toolbar-title>
          </v-toolbar>
          <v-card-text>
            <v-form @submit.prevent="login">
              <v-text-field 
                v-model="form.email" 
                label="Email" 
                outlined
              ></v-text-field>
              <v-text-field
                v-model="form.password"
                :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                :type="showPassword ? 'text' : 'password'"
                label="Mot de passe"
                outlined
                @click:append="showPassword = !showPassword"
              ></v-text-field>
              <v-btn 
                color="primary" 
                type="submit" 
                block 
                :loading="loading"
              >
                Se Connecter
              </v-btn>
            </v-form>
            
            <v-dialog v-model="dialog" max-width="400px">
              <v-card>
                <v-card-title class="headline">Erreur</v-card-title>
                <v-card-text>
                  {{ error || 'Erreur inconnue' }}
                </v-card-text>
                <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn color="primary" text @click="closeDialog">
                    Fermer
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data() {
    return {
      form: { 
        email: '', 
        password: '' 
      },
      loading: false,
      showPassword: false,
      dialog: false
    };
  },
  computed: {
    error() {
      return this.$store.state.auth.error;
    },
    userRole() {
      return this.$store.getters["auth/userRole"];
    }
  },
  watch: {
    error(newError) {
      if (newError) {
        this.dialog = true;
      }
    },
    userRole(newRole) {
      if (newRole === 'admin') {
        this.$router.push('/app/Conge');
      } else if (newRole === 'employe') {
        this.$router.push('/employee/pointage');
      }
    }
  },
  methods: {
    async login() {
      try {
        this.loading = true;
        await this.$store.dispatch('auth/clearError');
        await this.$store.dispatch('auth/login_user', this.form);
      } catch (error) {
        // Error handled by store
      } finally {
        this.loading = false;
      }
    },
    closeDialog() {
      this.dialog = false;
      this.$store.dispatch('auth/clearError');
    }
  }
};
</script>