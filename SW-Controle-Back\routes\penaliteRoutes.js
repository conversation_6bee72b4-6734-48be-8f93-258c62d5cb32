const express = require('express');
const router = express.Router();
const penaliteController = require('../controllers/penaliteController');
const { verifyToken, authorizeRoles } = require('../middleware/authMiddleware');

// POST create a penalite
router.post('/penalites', verifyToken, authorizeRoles('admin'), penaliteController.createPenalite);

// GET all penalites
router.get('/penalites', verifyToken, authorizeRoles('admin'), penaliteController.getPenalites);

// PUT update a penalite by ID
router.put('/penalites/:id', verifyToken, authorizeRoles('admin'), penaliteController.updatePenalite);

// DELETE delete a penalite by ID
router.delete('/penalites/:id', verifyToken, authorizeRoles('admin'), penaliteController.deletePenalite);

module.exports = router;
