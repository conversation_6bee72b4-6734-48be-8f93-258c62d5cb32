# Production Environment Configuration
NODE_ENV=production

# Database Configuration
DB_DIALECT=mysql
DB_HOST=your_production_db_host
DB_DATABASE=your_production_db_name
DB_USERNAME=your_production_db_user
DB_PASSWORD=your_production_db_password

# Server Configuration
PORT=3000

# Timezone for cron jobs (IMPORTANT for production)
TZ=Africa/Tunis

# Cron job settings
ENABLE_CRON_JOBS=true

# Additional production settings
# Add any other production-specific environment variables here
