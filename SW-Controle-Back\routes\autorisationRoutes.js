const express = require('express');
const router = express.Router();
const autorisationController = require('../controllers/autorisationController');
const { verifyToken, authorizeRoles } = require('../middleware/authMiddleware');

// Create a new autorisation
router.post('/autorisations', verifyToken, authorizeRoles('employe'), autorisationController.createAutorisation);

// Get all autorisations
router.get('/autorisations', verifyToken, authorizeRoles('admin'), autorisationController.getAllAutorisations);

// Update an autorisation
router.put('/autorisations/:id', verifyToken, authorizeRoles('employe'), autorisationController.updateAutorisation);

// Delete an autorisation
router.delete('/autorisations/:id', verifyToken, authorizeRoles('employe'), autorisationController.deleteAutorisation);

// Get autorisations for a specific user
router.get('/autorisation', verifyToken, authorizeRoles('employe'), autorisationController.getUserAutorisations);

// Toggle autorisation status
router.put('/autorisation/:id', verifyToken, authorizeRoles('admin'), autorisationController.toggleAutorisationStatus);

// Check autorisation status
router.get('/autorisation/status', verifyToken, authorizeRoles('employe'), autorisationController.checkAutorisationStatus);

module.exports = router;
