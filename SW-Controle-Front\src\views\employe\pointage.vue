<template>
  <v-container class="d-flex justify-center align-center" style="height: 80vh;">
    <!-- Card for Pointage -->
    <v-card v-if="!isCompleted && canPerformActions" class="pa-10" elevation="10" max-width="500">
      <!-- Date & Time -->
      <v-card-title class="text-h4 text-center">
        {{ formattedDate }}<br />
        {{ formattedTime }}
      </v-card-title>

      <!-- Loading, Errors, or Messages -->
      <v-card-text v-if="loading" class="text-center">
        <v-progress-circular indeterminate color="primary"></v-progress-circular>
      </v-card-text>
      <v-card-text v-else-if="error" class="text-center">
        <p>{{ error }}</p>
      </v-card-text>
      <v-card-text v-else-if="hasConge" class="text-center">
        <p>Vous ne pouvez pas pointer aujourd'hui, vous avez un congé.</p>
      </v-card-text>
      <v-card-text v-else-if="hasPenalite" class="text-center">
        <p>Vous ne pouvez pas pointer aujourd'hui, vous avez une pénalité.</p>
      </v-card-text>

      <!-- Main Content -->
      <v-card-text v-else class="text-center">
        <!-- Environment Selection -->
        <v-select
          v-if="!environmentSelected"
          v-model="selectedEnvironment"
          :items="environments"
          label="Choisissez votre environnement"
          variant="outlined"
          dense
          class="ma-4"
        ></v-select>
        <v-btn
          v-if="selectedEnvironment && !environmentSelected"
          @click="handleEnvironmentSelection"
          color="primary"
          class="ma-4"
          elevation="10"
          rounded
        >
          Suivant
        </v-btn>

        <!-- Pointage Buttons -->
        <div v-if="environmentSelected">
          <!-- Recurring Schedule (bool === true) -->
          <div v-if="!bool">
            <div class="mr-10" v-if="environmentSelected && !isCompleted && currentButton!=1">
              <v-btn
                @click="handleRefreshClick"
                icon
                color="primary"
                class="ml-4"
              >
                <v-icon>mdi-refresh</v-icon>
              </v-btn>
            </div>
            <v-btn
              v-if="currentButton === 1"
              @click="handleClick(1)"
              color="primary"
              class="ma-4"
              elevation="10"
              rounded
              size="x-large"
            >
              Début Matin
            </v-btn>
            <v-btn
              v-if="currentButton === 2"
              @click="handleClick(2)"
              color="success"
              class="ma-4"
              elevation="10"
              rounded
              size="x-large"
              :disabled="buttonDisabled.morningExit"
            >
              Fin Matin
            </v-btn>
            <v-btn
              v-if="currentButton === 3"
              @click="handleClick(3)"
              color="warning"
              class="ma-4"
              elevation="10"
              rounded
              size="x-large"
              :disabled="buttonDisabled.afternoonEntry"
            >
              Début Après Midi
            </v-btn>
            <v-btn
              v-if="currentButton === 4"
              @click="handleClick(4)"
              color="error"
              class="ma-4"
              elevation="10"
              rounded
              size="x-large"
              :disabled="buttonDisabled.afternoonExit"
            >
              Fin Après Midi
            </v-btn>
          </div>
          <!-- Non-Recurring Schedule (bool === false) -->
          <div v-else>
            <div class="mr-10" v-if="environmentSelected && !isCompleted">
              <v-btn
                @click="handleRefreshClick"
                icon
                color="primary"
                class="ml-4"
              >
                <v-icon>mdi-refresh</v-icon>
              </v-btn>
            </div>
            <v-btn
              v-if="currentButton === 1"
              @click="handleClick(1)"
              color="primary"
              class="ma-4"
              elevation="10"
              rounded
              size="x-large"
            >
              Début Matin
            </v-btn>
            <v-btn
              v-if="currentButton === 2"
              @click="handleClick(2)"
              color="error"
              class="ma-4"
              elevation="10"
              rounded
              size="x-large"
              :disabled="buttonDisabled.morningExit"
            >
              Fin Journée
            </v-btn>
          </div>
        </div>
      </v-card-text>
    </v-card>
    <v-card v-else class="pa-10" elevation="10" max-width="500">
      <v-card-title class="text-h4 text-center">
        Fin de pointage
      </v-card-title>
      <v-card-text v-if="!canPerformActions" class="text-center">
        <p>pas de pointage aprés 23:59.</p>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import moment from "moment";
import { mapActions, mapGetters } from "vuex";
import axios from "axios";

export default {
  data() {
    return {
      formattedDate: "",
      formattedTime: "",
      hasConge: false,
      hasPenalite: false,
      loading: true,
      error: null,
      currentButton: 1,
      isCompleted: false,
      selectedEnvironment: null,
      environments: ["sur site", "remote"],
      environmentSelected: false,
      presenceId: null,
      buttonStatus: {
        morningExit: false,
        afternoonEntry: false,
        afternoonExit: false,
      },
      apiurl: process.env.VUE_APP_API_URL,
      bool: true,
      canPerformActions: true,
    };
  },
  computed: {
    buttonDisabled() {
      return {
        morningExit: this.buttonStatus.morningExit,
        afternoonEntry: this.buttonStatus.afternoonEntry,
        afternoonExit: this.buttonStatus.afternoonExit,
      };
    },
    currentUserId() {
    return this.$store.getters['auth/currentUser']?.id;
  },
    ...mapGetters("schedule", ["isRecurring"]),
  },
  methods: {
    ...mapActions(["addPointage", "updatePresence"]),

    // Handle storage changes
    handleStorageChange(event) {
    if (event.key === "pointageData") {
      console.log("Local storage modified, force reloading state");
      this.loading = true;
      
      // Clear existing state
      this.presenceId = null;
      this.environmentSelected = false;
      this.selectedEnvironment = null;
      
      // Force fresh API reload
      this.fetchCurrentPresenceStatus()
        .finally(() => {
          this.loading = false;
        });
    }
  },

    // Save state to localStorage
    saveState() {
      const stateData = {
        presenceId: this.presenceId,
        selectedEnvironment: this.selectedEnvironment,
        environmentSelected: this.environmentSelected,
        currentButton: this.currentButton,
        isCompleted: this.isCompleted,
        buttonStatus: this.buttonStatus,
        lastPointageDate: this.formattedDate,
        updatedAt: Date.now(),
      };
      localStorage.setItem("pointageData", JSON.stringify(stateData));
    },

    // Restore state from localStorage with validation
    async restoreState() {
    // Always try backend first
    await this.fetchCurrentPresenceStatus();
    
    // Fallback to localStorage only if backend fails
    if (!this.presenceId) {
      const savedData = localStorage.getItem("pointageData");
      if (savedData) {
        try {
          const data = JSON.parse(savedData);
          if (data.lastPointageDate === this.formattedDate) {
            this.presenceId = data.presenceId;
            this.selectedEnvironment = data.selectedEnvironment;
            this.environmentSelected = data.environmentSelected;
            this.currentButton = data.currentButton;
            this.isCompleted = data.isCompleted;
            this.buttonStatus = data.buttonStatus;
          }
        } catch (e) {
          localStorage.removeItem("pointageData");
        }
      }
    }
  },


    // Fetch network time
    async fetchNetworkTime() {
      try {
        const response = await axios.get(`${this.apiurl}api/presence/getCurrentDateTime`, {
          headers: { Authorization: `Bearer ${localStorage.getItem("accessToken")}` },
          timeout: 5000, // Add timeout
        });
        const serverDateTime = new Date(response.data.currentDateTime);
        this.formattedDate = moment(serverDateTime).format("YYYY-MM-DD");
        this.formattedTime = moment(serverDateTime).format("HH:mm");
      } catch (error) {
        console.error("Failed to fetch network time:", error);
        this.formattedDate = moment().format("YYYY-MM-DD");
        this.formattedTime = moment().format("HH:mm:ss");
      }
    },

    // Check if time is past 23:59
    async isPast2359() {
      await this.fetchNetworkTime();
      const currentTime = moment(this.formattedTime, "HH:mm");
      return currentTime.hour() >= 23 && currentTime.minute() >= 59;
    },

    // Start network time interval
    startNetworkTimeInterval() {
      this.fetchNetworkTime();
      this.networkTimeInterval = setInterval(() => {
        this.fetchNetworkTime();
        this.checkIfPast2359(this.formattedTime);
      }, 60000); // 60,000 milliseconds = 1 minute
    },

    // Check if time is past 23:59 and reset state
    checkIfPast2359(time) {
      const currentTime = moment(time, "HH:mm");
      if (currentTime.hour() >= 23 && currentTime.minute() >= 59) {
        this.canPerformActions = false;
        this.resetPointageState();
      }
    },

    // Fetch button status
    async fetchButtonStatus() {
      if (this.presenceId && this.currentButton) {
        try {
          const response = await axios.get(
            `${this.apiurl}api/presence/checkButtonStatus`,
            {
              params: { presenceId: this.presenceId, buttonNumber: this.currentButton },
              headers: { Authorization: `Bearer ${localStorage.getItem("accessToken")}` },
              timeout: 5000, // Add timeout
            }
          );
          if (this.bool) {
            if (this.currentButton === 2) {
              this.buttonStatus.morningExit = response.data.disabled;
            }
          } else {
            switch (this.currentButton) {
              case 2:
                this.buttonStatus.morningExit = response.data.disabled;
                break;
              case 3:
                this.buttonStatus.afternoonEntry = response.data.disabled;
                break;
              case 4:
                this.buttonStatus.afternoonExit = response.data.disabled;
                break;
              default:
                break;
            }
          }
        } catch (error) {
          console.error("Failed to fetch button status:", error);
          this.error = "Failed to fetch button status.";
        }
      }
    },

    // Fetch conge status
    async fetchCongeToday() {
      try {
        const response = await axios.get(
          `${this.apiurl}api/presence/conge/today`,
          {
            headers: { Authorization: `Bearer ${localStorage.getItem("accessToken")}` },
            params: { UserId: this.currentUserId },
            timeout: 5000, // Add timeout
          }
        );
        this.hasConge = response.data.hasConge || false;
      } catch (error) {
        console.error("Failed to fetch conge status:", error);
        this.error = "Failed to fetch conge status.";
      }
    },

    // Fetch penalite status
    async fetchPenaliteToday() {
      try {
        const response = await axios.get(
          `${this.apiurl}api/presence/penalite/today`,
          {
            params: { UserId: this.currentUserId },
            headers: { Authorization: `Bearer ${localStorage.getItem("accessToken")}` },
            timeout: 5000, // Add timeout
          }
        );
        this.hasPenalite = response.data.hasPenalite || false;
      } catch (error) {
        console.error("Failed to fetch penalite status:", error);
        if (!this.error) {
          this.error = "Failed to fetch penalite status.";
        }
      }
    },

    // Handle refresh button click
    async handleRefreshClick() {
      if (this.presenceId && this.currentButton) {
        try {
          const response = await axios.get(
            `${this.apiurl}api/presence/checkButtonStatus`,
            {
              params: { presenceId: this.presenceId, buttonNumber: this.currentButton },
              headers: { Authorization: `Bearer ${localStorage.getItem("accessToken")}` },
              timeout: 5000, // Add timeout
            }
          );
          if (this.bool) {
            if (this.currentButton === 2) {
              this.buttonStatus.morningExit = response.data.disabled;
            }
          } else {
            switch (this.currentButton) {
              case 2:
                this.buttonStatus.morningExit = response.data.disabled;
                break;
              case 3:
                this.buttonStatus.afternoonEntry = response.data.disabled;
                break;
              case 4:
                this.buttonStatus.afternoonExit = response.data.disabled;
                break;
              default:
                break;
            }
          }
        } catch (error) {
          console.error("Failed to refresh button status:", error);
          this.error = "Failed to refresh button status.";
        }
      }
    },

    // Fetch current presence status with error handling
    async fetchCurrentPresenceStatus() {
    try {
      const response = await axios.get(
        `${this.apiurl}api/presence/current-status`,
        {
          headers: { Authorization: `Bearer ${localStorage.getItem("accessToken")}` },
          timeout: 5000,
        }
      );

      // Always reset state before applying backend response
      this.resetPointageState();
      
      if (response.data?.presenceId) {
        const { 
          presenceId,
          environmentSelected,
          currentButton,
          isCompleted,
          buttonStatus,
          scheduleType
        } = response.data;

        // Apply backend state
        this.presenceId = presenceId;
        this.environmentSelected = environmentSelected;
        this.currentButton = currentButton;
        this.isCompleted = isCompleted;
        this.buttonStatus = buttonStatus || this.buttonStatus;
        this.bool = !scheduleType;

        // Sync to localStorage
        this.saveState();
      }
    } catch (error) {
      if (error.response?.status === 401) this.$router.push("/login");
      console.error("Presence fetch error:", error);
    }
  },



    // Handle environment selection
    async handleEnvironmentSelection() {
      if (!this.canPerformActions) {
        this.error = "Pointage cannot be performed after 23:59.";
        return;
      }
      try {
        this.environmentSelected = true;
        const response = await this.addPointage({
          env: this.selectedEnvironment,
          date: new Date(),
          status: "en attente",
          UserId: this.currentUserId,
        });
        this.presenceId = response;
        await this.fetchCurrentPresenceStatus();
        await this.saveState();
      } catch (error) {
        console.error("Failed to handle environment selection:", error);
        this.error = "Failed to handle environment selection.";
      }
    },

    // Handle button clicks
    async handleClick(buttonNumber) {
      if (!this.canPerformActions) {
        this.error = "pas de pointage aprés 23:59.";
        return;
      }
      try {
        const fieldMap = { 1: "entree", 2: "sortie", 3: "entree1", 4: "sortie1" };
        const field = fieldMap[buttonNumber];
        const updateFields = {};
        updateFields[field] = true;
        await this.updatePresence({
          id: this.presenceId,
          userId: this.currentUserId,
          ...updateFields,
        });
        await this.fetchButtonStatus();
        if (!this.bool) {
          if (buttonNumber < 4) {
            this.currentButton++;
          } else {
            this.isCompleted = true;
          }
        } else {
          if (buttonNumber === 1) {
            this.currentButton = 2;
          } else if (buttonNumber === 2) {
            this.isCompleted = true;
          }
        }
        this.saveState();
      } catch (error) {
        console.error("Failed to handle button click:", error);
        this.error = "Failed to handle button click.";
      }
    },

    // Load bool (Ramadan schedule)
    async loadbool() {
      try {
        const response = await axios.get(
          `${this.apiurl}api/schedules/getisramadan`,
          {
            params: { date: new Date() },
            headers: { Authorization: `Bearer ${localStorage.getItem("accessToken")}` },
            timeout: 5000, // Add timeout
          }
        );
        this.bool = response.data.isRamadan;
      } catch (error) {
        console.error("Failed to load bool:", error);
        this.error = "Failed to load schedule type.";
      }
    },

    // Reset pointage state
    resetPointageState() {
      this.presenceId = null;
      this.selectedEnvironment = null;
      this.environmentSelected = false;
      this.currentButton = 1;
      this.isCompleted = false;
      this.buttonStatus = {
        morningExit: false,
        afternoonEntry: false,
        afternoonExit: false,
      };
      localStorage.removeItem("pointageData");
    },
  },
  watch: {
    presenceId: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.fetchButtonStatus();
        }
      },
    },
    currentButton: {
      handler(newValue) {
        if (this.presenceId && newValue) {
          this.fetchButtonStatus();
        }
      },
    },
  },
  async created() {
  window.addEventListener("storage", this.handleStorageChange);

  try {
    await this.startNetworkTimeInterval();
    await Promise.all([
      this.fetchCongeToday(),
      this.fetchPenaliteToday(),
      this.loadbool()
    ]);

    this.canPerformActions = !(await this.isPast2359());
    
    // Always restore from backend first
    await this.restoreState();

  } catch (error) {
    console.error("Initialization error:", error);
    this.error = "Initialization failed. Please refresh.";
  } finally {
    this.loading = false;
  }
},
  beforeDestroy() {
    window.removeEventListener("storage", this.handleStorageChange);
    clearInterval(this.networkTimeInterval);
  },
};
</script>