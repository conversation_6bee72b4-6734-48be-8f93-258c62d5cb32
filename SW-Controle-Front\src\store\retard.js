import axios from 'axios';

const getAccessToken = () => localStorage.getItem('accessToken');

const state = {
retard: [],
total:null,
loading: false,
};

const getters = {
  retard: (state) => state.retard,
  total: (state) => state.total,
  loading: (state) => state.loading,
};

const actions = {
  async fetchRetard({ commit }, { userId, month, page, limit, sortBy, order ,year}) {
    commit('SET_LOADING', true);
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/retard`, {
        params: { userId, month, page, limit, sortBy, order,year },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
        },
      });
     
      commit('SET_RETARDS', response.data.records);
      commit('SET_TOTAL', response.data.totalRecords);
    } catch (error) {
      console.error('Error fetching retard:', error);
    } finally {
      commit('SET_LOADING', false);
    }
  },
};

const mutations = {
  SET_RETARDS(state, retard) {
    state.retard = retard;
  },
  SET_TOTAL(state, total) {
    state.total = total;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
};

export default {
  namespaced: true,  // Add this line to enable namespacing
  state,
  getters,
  actions,
  mutations,
};