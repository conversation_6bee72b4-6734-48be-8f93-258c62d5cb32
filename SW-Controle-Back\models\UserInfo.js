module.exports = (sequelize, DataTypes) => {
  const UserInfo = sequelize.define('UserInfo', {
    months: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    soldeConge: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    soldeAncienConge: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    congePrise: { // New field
      type: DataTypes.INTEGER,
      allowNull: true
    },
    sanctions: { // New field
      type: DataTypes.INTEGER,
      allowNull: true
    },
    resteConge: { // New field
      type: DataTypes.INTEGER,
      allowNull: true
    },

    previousTotalUsage: { // Existing new field
      type: DataTypes.INTEGER,
      allowNull: true
    },
    isSoldeAncienCongeManual: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    lastCalculatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    year:
    {
      type: DataTypes.INTEGER,
      allowNull: true
    }

  }, {
    timestamps: true // Enable timestamps to include updatedAt
  });

  UserInfo.associate = function (models) {
    UserInfo.belongsTo(models.User, { foreignKey: 'UserId', onDelete: 'CASCADE' });
  };

  return UserInfo;
};