import axios from 'axios';
const getAccessToken = () => localStorage.getItem('accessToken');

const state = {
  schedules: [],
  error: null,
  loading: false,
  isselectedschedule:{},
  isRecurring: null,
  schedule:null,
  aschedule:{}
}; 

const getters = {
  getSchedules: state => state.schedules,
  getError: state => state.error,
  isLoading: state => state.loading,
  selectedschedule: state=>state.isselectedschedule,
  isRecurring: state => state.isRecurring,
  scheduleSelected: state => state.aschedule,
  
};

const mutations = {
  setSchedules(state, schedules) {
    state.schedules = schedules;
  },
  setError(state, error) {
    state.error = error;
  },
  setLoading(state, loading) {
    state.loading = loading; // Added loading mutation
  },
  UPDATE_SCHEDULE(state, updatedSchedule) {
    const index = state.schedules.findIndex(schedule => schedule.id === updatedSchedule.id);
    if (index !== -1) {
      state.schedules.splice(index, 1, updatedSchedule);
    }
  },
  SET_IS_RECURRING(state, isRecurring) {
    state.isRecurring= isRecurring;
  },
  DELETE_SCHEDULE(state, id) {
    state.schedules = state.schedules.filter(schedule => schedule.id !== id);
  },
  SET_SCHEDULE(state,schedule){
    state.schedule=schedule
  },
  SET_Usage(state,schedule){
    state.aschedule=schedule
  },
};

const actions = {
  async fetchSchedules({ commit }) {
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/schedules`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
        },
      });
     
      commit('setSchedules', response.data);
    } catch (error) {
      console.error('Failed to fetch schedules:', error.message);
    }
  },
  async createSchedule({ commit }, schedule) {
    commit('setLoading', true); 
    try {
      const response = await axios.post(`${process.env.VUE_APP_API_URL}api/schedules`, schedule, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
       // Adjusted to use response.data.data
      return { success:response.data, message: response.data.message }; // Return success message
    } catch (error) {
      return { success: error.response.data.success, message: error.response.data.message };
    } finally {
      commit('setLoading', false); 
    }
  },
  async updateSchedule({ commit }, schedule) {
    commit('setLoading', true);
    try {
      const response = await axios.put(`${process.env.VUE_APP_API_URL}api/schedules/${schedule.id}`, schedule, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      return { success: true, message: response.data.message }; 
    } catch (error) {
      console.log(error.response.data.message)
      return { success:error.response.data.success, message: error.response.data.message };
    } finally {
      commit('setLoading', false); 
    }
  },
  async deleteSchedule({ commit }, id) {
    commit('setLoading', true); 
    try {
    const response =await axios.delete(`${process.env.VUE_APP_API_URL}api/schedules/${id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      return { success: response.data.success, message: response.data.message }; 
    } catch (error) {
      return { success:error.response.data.success, message: error.response.data.message };
    } finally {
      commit('setLoading', false); 
    }
  },
  async toggleSelected({ commit }, ScheduleId) {
    try {
      await axios.put(`${process.env.VUE_APP_API_URL}api/schedules/toggle-selected/${ScheduleId}`,{}, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });

    } catch (error) {
      commit('setError', error.message);
      console.error('Error toggling isSelected:', error);
      throw error;
    }
  },
  async checkIfScheduleIsRecurring({ commit },date) {
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/schedules/getisramadan`, {
        params: { date },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      commit('SET_IS_RECURRING', reponse.data.isRamadan);
    } catch (error) {
      console.log(error)
    }
  },
  async fetchSelectedSchedule({ commit }) {
    try {
      const response = await axios.get(`${process.env.VUE_APP_API_URL}api/schedules/getisselectedschedule`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      console.log(response.data.schedule.id)
      commit('SET_Usage', response.data.schedule);
    } catch (error) {
      commit('SET_ERROR', error.message);
    } finally {
      commit('SET_LOADING', false);
    }
  },

};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};