{"ast": null, "code": "import { createStore } from 'vuex';\nimport auth from './auth';\nimport agent from './agent';\nimport schedule from './schedule';\nimport projects from './projet';\nimport presence from './presence';\nimport penalite from './penalite';\nimport autorisation from './autorisation';\nimport conge from './conge';\nimport absence from './absence';\nimport retard from './retard';\nimport CalculeConge from './CalculeConge';\nimport salary from './salary';\nexport default createStore({\n  modules: {\n    auth,\n    conge,\n    agent,\n    schedule,\n    projects,\n    presence,\n    penalite,\n    autorisation,\n    absence,\n    retard,\n    CalculeConge\n  }\n});", "map": {"version": 3, "names": ["createStore", "auth", "agent", "schedule", "projects", "presence", "penalite", "autorisation", "conge", "absence", "retard", "CalculeConge", "salary", "modules"], "sources": ["C:/Users/<USER>/Desktop/swcontrole/SW-Controle-Front/src/store/index.js"], "sourcesContent": ["import { createStore } from 'vuex';\r\nimport auth from './auth';\r\nimport agent from './agent';\r\nimport schedule from './schedule';\r\nimport projects from './projet';\r\nimport presence from './presence'\r\nimport penalite from './penalite'\r\nimport autorisation from './autorisation'\r\nimport conge from './conge'\r\nimport absence from './absence'\r\nimport retard from './retard'\r\nimport CalculeConge from './CalculeConge'\r\nimport salary from './salary'\r\nexport default createStore({\r\n  modules: {\r\n    auth,\r\n    conge,\r\n    agent,\r\n    schedule,\r\n    projects,\r\n    presence,\r\n    penalite,\r\n    autorisation,\r\n    absence,\r\n    retard,\r\n    CalculeConge\r\n  }\r\n});"], "mappings": "AAAA,SAASA,WAAW,QAAQ,MAAM;AAClC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,MAAM,UAAU;AAC7B,eAAeZ,WAAW,CAAC;EACzBa,OAAO,EAAE;IACPZ,IAAI;IACJO,KAAK;IACLN,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,YAAY;IACZE,OAAO;IACPC,MAAM;IACNC;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}