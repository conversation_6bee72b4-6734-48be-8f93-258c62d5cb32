const express = require('express');
const router = express.Router();
const calculeController = require('../controllers/calculeController');
const { verifyToken, authorizeRoles } = require('../middleware/authMiddleware');

// POST create a new absence
router.post('/absence', verifyToken, authorizeR<PERSON>s('admin'), calculeController.addAbsence);

// GET all absences
router.get('/absence', verifyToken, authorizeRoles('admin'), calculeController.getAbsences);

// GET tardiness data
router.get('/retard', verifyToken, authorizeRoles('admin', 'employe'), calculeController.getRetardData);

// GET leave balance calculation
router.get('/congecalcule', verifyToken, authorizeRoles('admin'), calculeController.calculateLeaveBalance);

// GET leave balance for a specific user
router.get('/calculep', verifyToken, authorizeRoles('admin', 'employe'), calculeController.calculateLeaveBalanceForUser);

// PUT update leave balance for previous years
router.put('/update-solde-ancien-conge', verifyToken, authorizeRoles('admin'), calculeController.updateSoldeAncienConge);

module.exports = router;
