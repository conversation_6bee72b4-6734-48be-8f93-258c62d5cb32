<template>
  <v-container>
    <v-card elevation="1">
      <v-card-title class="text-h5 pa-4">
        Calcule des Salaires - {{ monthName }} {{ currentYear }}
      </v-card-title>

      <v-card-subtitle class="px-4 pb-2">
        <v-alert type="info" variant="tonal" density="compact">
          <strong>Règles de déduction (par jour):</strong>
          10-30min = 1h déduction | 30min-1h = 2h déduction | 1-2h = 3h déduction | >2h = 4h déduction
        </v-alert>
      </v-card-subtitle>
      
      <!-- Filters and Controls -->
      <v-row class="pa-4" align="center">
        <v-col cols="12" md="3">
          <v-select
            v-model="selectedMonth"
            :items="months"
            label="Mois"
            density="compact"
            variant="outlined"
            @update:model-value="fetchData"
          ></v-select>
        </v-col>
        <v-col cols="12" md="3">
          <v-select
            v-model="selectedYear"
            :items="years"
            label="Année"
            density="compact"
            variant="outlined"
            @update:model-value="fetchData"
          ></v-select>
        </v-col>
        <v-col cols="12" md="3">
          <v-select
            v-model="selectedAgent"
            :items="agentOptions"
            label="Agent (Optionnel)"
            density="compact"
            variant="outlined"
            clearable
            @update:model-value="fetchData"
          ></v-select>
        </v-col>
        <v-col cols="12" md="3">
          <v-btn @click="exportToExcel" color="green" prepend-icon="mdi-file-excel">
            Export Excel
          </v-btn>
        </v-col>
      </v-row>

      <!-- Data Table -->
      <v-data-table-server
        :headers="headers"
        :items="salaryData"
        :items-length="total"
        :loading="loading"
        @update:options="updateOptions"
        class="elevation-1"
      >
        <template v-slot:item.baseSalary="{ item }">
          {{ formatCurrency(item.baseSalary) }}
        </template>
        
        <template v-slot:item.totalRetardFormatted="{ item }">
          <v-chip 
            :color="getRetardColor(item.totalRetardMinutes)" 
            size="small"
          >
            {{ item.totalRetardFormatted }}
          </v-chip>
        </template>
        
        <template v-slot:item.deductedHours="{ item }">
          <v-chip 
            :color="item.deductedHours > 0 ? 'red' : 'green'" 
            size="small"
          >
            {{ item.deductedHours }}h
          </v-chip>
        </template>
        
        <template v-slot:item.deductedAmount="{ item }">
          <span :class="item.deductedAmount > 0 ? 'text-red' : 'text-green'">
            {{ formatCurrency(item.deductedAmount) }}
          </span>
        </template>
        
        <template v-slot:item.remainingSalary="{ item }">
          <strong class="text-primary">
            {{ formatCurrency(item.remainingSalary) }}
          </strong>
        </template>
        
        <template v-slot:item.actions="{ item }">
          <v-btn
            icon="mdi-eye"
            size="small"
            @click="viewDetails(item)"
            variant="text"
          ></v-btn>
        </template>
      </v-data-table-server>
    </v-card>

    <!-- Details Dialog -->
    <v-dialog v-model="detailsDialog" max-width="800px">
      <v-card v-if="selectedItem">
        <v-card-title>
          Détails - {{ selectedItem.name }}
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col cols="6">
              <v-list>
                <v-list-item>
                  <v-list-item-title>Salaire de base:</v-list-item-title>
                  <v-list-item-subtitle>{{ formatCurrency(selectedItem.baseSalary) }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>Retard total:</v-list-item-title>
                  <v-list-item-subtitle>{{ selectedItem.totalRetardFormatted }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>Heures déduites:</v-list-item-title>
                  <v-list-item-subtitle>{{ selectedItem.deductedHours }}h</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>Montant déduit:</v-list-item-title>
                  <v-list-item-subtitle class="text-red">{{ formatCurrency(selectedItem.deductedAmount) }}</v-list-item-subtitle>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title>Salaire restant:</v-list-item-title>
                  <v-list-item-subtitle class="text-primary font-weight-bold">{{ formatCurrency(selectedItem.remainingSalary) }}</v-list-item-subtitle>
                </v-list-item>
              </v-list>
            </v-col>
            <v-col cols="6">
              <v-card-subtitle>Détail des déductions par jour:</v-card-subtitle>
              <v-list v-if="selectedItem.retardByDays && selectedItem.retardByDays.length > 0">
                <v-list-item v-for="day in selectedItem.retardByDays" :key="day.date">
                  <template v-slot:prepend>
                    <v-chip
                      :color="getRetardColor(day.retardMinutes)"
                      size="small"
                    >
                      {{ day.retardFormatted }}
                    </v-chip>
                  </template>
                  <v-list-item-title>{{ day.date }}</v-list-item-title>
                  <v-list-item-subtitle>
                    Déduction: {{ day.deductedHours }}h
                    <span v-if="day.deductedHours > 0" class="text-red">
                      ({{ formatCurrency(day.deductedAmount) }})
                    </span>
                  </v-list-item-subtitle>
                </v-list-item>
              </v-list>
              <v-alert v-else type="success" variant="tonal">
                Aucun retard ce mois-ci
              </v-alert>

              <!-- Summary of daily deductions -->
              <v-divider class="my-3"></v-divider>
              <v-card-subtitle>Résumé des déductions:</v-card-subtitle>
              <v-list-item>
                <v-list-item-title>Total jours avec retard:</v-list-item-title>
                <v-list-item-subtitle>{{ selectedItem.retardByDays ? selectedItem.retardByDays.length : 0 }} jours</v-list-item-subtitle>
              </v-list-item>
              <v-list-item>
                <v-list-item-title>Total heures déduites:</v-list-item-title>
                <v-list-item-subtitle class="text-red">{{ selectedItem.deductedHours }}h</v-list-item-subtitle>
              </v-list-item>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="blue-darken-1" variant="text" @click="detailsDialog = false">
            Fermer
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar for notifications -->
    <v-snackbar v-model="snackbar" :color="snackbarColor" timeout="3000">
      {{ snackbarText }}
    </v-snackbar>
  </v-container>
</template>

<script>
import { mapGetters } from 'vuex';
import * as XLSX from 'xlsx';

export default {
  name: 'SalaryCalculation',
  data() {
    return {
      selectedMonth: new Date().getMonth() + 1,
      selectedYear: new Date().getFullYear(),
      selectedAgent: null,
      detailsDialog: false,
      selectedItem: null,
      snackbar: false,
      snackbarText: '',
      snackbarColor: 'success',
      options: {},
      headers: [
        { title: "Agent", key: "name", sortable: true },
        { title: "Email", key: "email", sortable: false },
        { title: "Salaire de base", key: "baseSalary", sortable: true },
        { title: "Retard total", key: "totalRetardFormatted", sortable: false },
        { title: "Heures déduites", key: "deductedHours", sortable: false },
        { title: "Montant déduit", key: "deductedAmount", sortable: false },
        { title: "Salaire restant", key: "remainingSalary", sortable: false },
        { title: "Actions", key: "actions", sortable: false }
      ],
      months: [
        { title: 'Janvier', value: 1 },
        { title: 'Février', value: 2 },
        { title: 'Mars', value: 3 },
        { title: 'Avril', value: 4 },
        { title: 'Mai', value: 5 },
        { title: 'Juin', value: 6 },
        { title: 'Juillet', value: 7 },
        { title: 'Août', value: 8 },
        { title: 'Septembre', value: 9 },
        { title: 'Octobre', value: 10 },
        { title: 'Novembre', value: 11 },
        { title: 'Décembre', value: 12 }
      ]
    };
  },
  computed: {
    ...mapGetters('salary', ['salaryData', 'loading', 'total', 'currentMonth', 'currentYear']),
    ...mapGetters('agent', ['allAgents']),
    
    monthName() {
      const month = this.months.find(m => m.value === this.selectedMonth);
      return month ? month.title : '';
    },
    
    years() {
      const currentYear = new Date().getFullYear();
      const years = [];
      for (let i = currentYear - 2; i <= currentYear + 1; i++) {
        years.push(i);
      }
      return years;
    },
    
    agentOptions() {
      return this.allAgents.map(agent => ({
        title: agent.name,
        value: agent.id
      }));
    }
  },
  
  methods: {
    async fetchData() {
      const { page, itemsPerPage, sortBy } = this.options;
      const sortKey = sortBy && sortBy.length > 0 ? sortBy[0].key : 'name';
      const sortOrder = sortBy && sortBy.length > 0 ? sortBy[0].order : 'asc';

      await this.$store.dispatch('salary/fetchSalaryData', {
        userId: this.selectedAgent,
        month: this.selectedMonth,
        year: this.selectedYear,
        page: page || 1,
        limit: itemsPerPage || 10,
        sortBy: sortKey,
        order: sortOrder
      });
    },
    
    updateOptions(newOptions) {
      this.options = newOptions;
      this.fetchData();
    },
    
    viewDetails(item) {
      this.selectedItem = item;
      this.detailsDialog = true;
    },
    
    formatCurrency(amount) {
      return new Intl.NumberFormat('fr-TN', {
        style: 'currency',
        currency: 'TND'
      }).format(amount || 0);
    },
    
    getRetardColor(minutes) {
      if (minutes === 0) return 'green';
      if (minutes < 30) return 'orange';
      if (minutes < 60) return 'deep-orange';
      return 'red';
    },
    
    async exportToExcel() {
      try {
        const data = await this.$store.dispatch('salary/exportSalaryToExcel', {
          userId: this.selectedAgent,
          month: this.selectedMonth,
          year: this.selectedYear
        });

        const worksheet = XLSX.utils.json_to_sheet(data.map(item => ({
          'Agent': item.name,
          'Email': item.email,
          'Salaire de base': item.baseSalary,
          'Retard total (minutes)': item.totalRetardMinutes,
          'Retard total': item.totalRetardFormatted,
          'Jours avec retard': item.retardByDays ? item.retardByDays.length : 0,
          'Total heures déduites': item.deductedHours,
          'Montant total déduit': item.deductedAmount,
          'Salaire restant': item.remainingSalary,
          'Taux horaire': item.hourlyRate,
          'Mois': item.month,
          'Année': item.year
        })));

        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Calcul Salaires');
        
        const fileName = `calcul_salaires_${this.monthName}_${this.selectedYear}.xlsx`;
        XLSX.writeFile(workbook, fileName);
        
        this.showSnackbar('Export Excel réussi', 'success');
      } catch (error) {
        console.error('Error exporting to Excel:', error);
        this.showSnackbar('Erreur lors de l\'export Excel', 'error');
      }
    },
    
    showSnackbar(text, color) {
      this.snackbarText = text;
      this.snackbarColor = color;
      this.snackbar = true;
    }
  },
  
  async mounted() {
    await this.$store.dispatch('agent/fetchAllAgents');
    this.fetchData();
  }
};
</script>

<style scoped>
.text-red {
  color: #f44336 !important;
}

.text-green {
  color: #4caf50 !important;
}
</style>
