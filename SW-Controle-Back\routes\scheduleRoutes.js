const express = require('express');
const router = express.Router();
const scheduleController = require('../controllers/scheduleController');
const { verifyToken, authorizeRoles } = require('../middleware/authMiddleware');

// GET all schedules
router.get('/', verifyToken, authorizeRoles('admin'), scheduleController.getAllSchedules);

// POST create a new schedule
router.post('/', verifyToken, authorize<PERSON><PERSON><PERSON>('admin'), scheduleController.createSchedule);

// PUT update a schedule
router.put('/:id', verifyToken, authorizeRoles('admin'), scheduleController.updateSchedule);

// DELETE delete a schedule
router.delete('/:id', verifyToken, authorizeRoles('admin'), scheduleController.deleteSchedule);

// GET the currently selected schedule
router.get('/getisselectedschedule', verifyToken, authorizeR<PERSON>s('employe'), scheduleController.getCurrentSchedule);

// GET Ramadan schedule information
router.get('/getis<PERSON>dan', verifyToken, authorizeR<PERSON>s('admin', 'employe'), scheduleController.isRamadanSchedule);

module.exports = router;