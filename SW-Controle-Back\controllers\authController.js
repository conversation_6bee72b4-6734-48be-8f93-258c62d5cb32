const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt'); // Added for secure password hashing
const { User } = require('../models');
const { Op } = require('sequelize');
const nodemailer = require('nodemailer');
const crypto = require('crypto');
const moment = require('moment-timezone');
const { addToBlacklist } = require('../middleware/authBlackList/blacklist');
exports.logout = async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1]; // Bearer token format

  if (!token) {
    return res.status(400).json({ error: 'Token is missing' });
  }

  addToBlacklist(token);
    const decoded = jwt.verify(token, 'your_access_token_secret');

    // Immediately invalidate the token by overwriting its expiration
    jwt.sign(
      { ...decoded, exp: Math.floor(Date.now() / 1000) }, 
      'your_access_token_secret'
    );
  return res.status(200).json({ message: 'Logged out successfully' });
};
exports.login = async (req, res) => {
  const { email, password } = req.body;

  try {
    const user = await User.findOne({ where: { email } });

    if (!user) {
      return res.status(404).json({ message: 'Adresse e-mail non trouvée' });
    }

    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
        return res.status(401).json({ message: 'Mot de passe incorrect' });
    }

    const accessToken = jwt.sign({ id: user.id,role: user.role  }, 'your_access_token_secret');// Consider adding expiration for refresh tokens

    res.status(200).json({ accessToken, user });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Erreur du serveur' });
  }
};
exports.forgotPassword=async (req,res)=>{
   try{
        const user = await User.findOne({ where: { email: req.body.email } });
        if (!user) {
            return res.status(404).send('User not found');
        }

        // Generate reset token
        const resetToken = crypto.randomBytes(32).toString('hex');
        const resetTokenExpiration = Date.now() + 3600000; // 1 hour
        const formattedExpiration = moment(resetTokenExpiration).tz('GMT').add(1, 'hours').format();
        // Save token and expiration in the database
        user.resetPasswordToken = resetToken;
        user.resetPasswordExpires = formattedExpiration;
        await user.save();

       
        const resetUrl = `http://localhost:8080/reset-password/${resetToken}`;
  const sendEmail=async (options)=>{
    const transporter = nodemailer.createTransport({
      host: 'smtp.office365.com',
      port: 587,
      secure: false, 
      auth: {
        user: '<EMAIL>',
        pass: 'ezaAZE@10121998'
      }
    });
    const emailOptions = {
      from: '<EMAIL>',
      to: options.email,
      subject: 'Création de votre mot de passe',
      text: `Bonjour,
  
  Un administrateur a configuré un mot de passe pour votre compte. Voici vos informations de connexion :
  
  Votre mot de passe : ${password}
  
  Veuillez utiliser ce mot de passe pour vous connecter à votre compte. Par mesure de sécurité, nous vous recommandons de le modifier dès votre première connexion.
  
  Si vous n'avez pas demandé la création de ce compte ou si vous avez des questions, veuillez contacter l'équipe de support de swcontorle.
  
  Cordialement,
  L'équipe de support deswcontrole
  `,
  };
    await transporter.sendMail(emailOptions)
 }

  res.status(200).json({'message':'Password reset token sent to email'});
} catch (error) {
    res.status(500).json({'message':'error reseting'});
}
} 
exports.resetPassword = async (req, res) => {
  try {
    const adjustedDate = moment().tz('GMT').add(1, 'hours').toDate();
    const user = await User.findOne({
      where: {
        resetPasswordToken: req.params.token,
        resetPasswordExpires: { [Op.gt]: adjustedDate },
      },
    });

    if (!user) {
      return res.status(400).send('Password reset token is invalid or has expired');
    }

    const { password } = req.body;

    if (!password || password.length < 8) {
      return res.status(400).send('Password must be at least 8 characters long');
    }

    user.password = await bcrypt.hash(password, 12);
    user.resetPasswordToken = null;
    user.resetPasswordExpires = null;
    await user.save();

    res.status(200).send('Password has been reset');
  } catch (error) {
    res.status(500).send(error);
    console.log(error);
  }
};
exports.resetPasswordProfile = async (req, res) => {
  try {
    const { id, password } = req.body;

    // Validate input
    if (!id || !password) {
      return res.status(400).json({
        success: false,
        message: 'ID utilisateur et mot de passe sont requis',
        type: 'error'
      });
    }

    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Le mot de passe doit contenir au moins 8 caractères',
        type: 'error'
      });
    }

    // Find user
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'Utilisateur non trouvé',
        type: 'error'
      });
    }

    // Hash and update password
    const hashedPassword = await bcrypt.hash(password, 12);
    await user.update({ password: hashedPassword });

    res.status(200).json({ 
      success: true, 
      message: 'Mot de passe mis à jour avec succès',
      type: 'success'
    });
  } catch (error) {
    console.error('Reset password profile error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Une erreur inattendue est survenue',
      type: 'error'
    });
  }
};